package controllers

import (
	"strconv"
	"web_app/logic"
	"web_app/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// VoteHandler 用户投票接口
// @Summary 帖子投票
// @Description 用户为帖子投票接口
// @Tags 投票相关接口
// @Accept json
// @Produce json
// @Param object body models.ParamVote true "投票参数"
// @Success 200 {object} controllers.ResponseData
// @Security ApiKeyAuth
// @Router /vote [post]
func VoteHandler(c *gin.Context) {
	zap.L().Info("收到投票请求")

	// 获取当前用户ID
	userID, err := GetCurrentUserID(c)
	if err != nil {
		zap.L().Error("获取用户ID失败", zap.Error(err))
		ResponseError(c, CodeNeedLogin, GetMsg(CodeNeedLogin))
		return
	}
	zap.L().Info("用户ID获取成功", zap.Int64("user_id", userID))

	// 绑定参数
	var param models.ParamVote
	if err := c.ShouldBindJSON(&param); err != nil {
		zap.L().Error("VoteHandler bind param failed", zap.Error(err))
		ResponseErrorWithMsg(c, CodeInvalidParam, err.Error())
		return
	}
	zap.L().Info("参数绑定成功",
		zap.Int64("post_id", param.PostID),
		zap.Int8("direction", param.Direction))

	// 参数验证
	if param.PostID <= 0 {
		zap.L().Error("帖子ID无效", zap.Int64("post_id", param.PostID))
		ResponseError(c, CodeInvalidParam, "帖子ID无效")
		return
	}

	if param.Direction < -1 || param.Direction > 1 {
		zap.L().Error("投票方向无效", zap.Int8("direction", param.Direction))
		ResponseError(c, CodeInvalidParam, "投票方向无效")
		return
	}

	// 调用业务逻辑
	zap.L().Info("开始调用投票业务逻辑")
	voteData, err := logic.VoteForPost(userID, param.PostID, param.Direction)
	if err != nil {
		zap.L().Error("VoteHandler logic.VoteForPost failed",
			zap.Int64("user_id", userID),
			zap.Int64("post_id", param.PostID),
			zap.Int8("direction", param.Direction),
			zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}

	zap.L().Info("投票成功",
		zap.Int64("user_id", userID),
		zap.Int64("post_id", param.PostID),
		zap.Int8("direction", param.Direction),
		zap.Int64("vote_num", voteData.VoteNum),
		zap.Int8("user_vote", voteData.UserVote))

	ResponseSuccess(c, voteData)
}

// GetPostVoteHandler 获取帖子投票信息
func GetPostVoteHandler(c *gin.Context) {
	// 获取帖子ID
	postIDStr := c.Param("id")
	postID, err := strconv.ParseInt(postIDStr, 10, 64)
	if err != nil {
		ResponseError(c, CodeInvalidParam, "帖子ID格式错误")
		return
	}

	// 获取当前用户ID（可选）
	userID, _ := GetCurrentUserID(c)

	// 调用业务逻辑
	voteData, err := logic.GetPostVoteInfo(postID, userID)
	if err != nil {
		zap.L().Error("GetPostVoteHandler logic.GetPostVoteInfo failed",
			zap.Int64("post_id", postID),
			zap.Int64("user_id", userID),
			zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}

	ResponseSuccess(c, voteData)
}
