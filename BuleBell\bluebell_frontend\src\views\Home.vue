<template>
  <div class="content">
    <div class="left">
      <!-- <h4 class="c-l-title">热门帖子</h4> -->
      <div class="c-l-header">
        <div class="new btn-iconfont"
        :class="{ active: timeOrder }"
        @click="selectOrder('time')"
        >
          <i class="iconfont icon-polygonred"></i>New
        </div>
        <div class="top btn-iconfont"
         :class="{ active: scoreOrder }"
         @click="selectOrder('score')"
        >
          <i class="iconfont icon-top"></i>Score
        </div>
        <button class="btn-publish" @click="goPublish">发表</button>
      </div>
      <ul class="c-l-list">
        <li class="c-l-item"  v-for="post in postList" :key="post.post_id">
          <div class="post">
            <a class="vote upvote"
               :class="{
                 active: post.user_vote === 1,
                 loading: post.voting
               }"
               @click="vote(post.post_id, 1)"
               :title="post.user_vote === 1 ? '取消赞' : '赞'"
            >
              <span v-if="!post.voting" class="iconfont icon-up"></span>
              <span v-else class="loading-spinner">⟳</span>
            </a>
            <span class="text"
                  :class="{
                    positive: (post.vote_num || 0) > 0,
                    negative: (post.vote_num || 0) < 0
                  }"
            >
              {{post.vote_num || 0}}
            </span>
            <a class="vote downvote"
               :class="{
                 active: post.user_vote === -1,
                 loading: post.voting
               }"
               @click="vote(post.post_id, -1)"
               :title="post.user_vote === -1 ? '取消踩' : '踩'"
            >
              <span v-if="!post.voting" class="iconfont icon-down"></span>
              <span v-else class="loading-spinner">⟳</span>
            </a>
          </div>
          <div class="l-container" @click="goDetail(post.post_id)">
            <h4 class="con-title">{{post.title}}</h4>
            <div class="con-memo">
              <p>{{post.content}}</p>
            </div>
            <div class="post-info">
              <span class="author">作者: {{post.author_name}}</span>
              <span class="community">社区: {{post.community_name}}</span>
            </div>
            <!-- <div class="user-btn">
              <span class="btn-item">
                <i class="iconfont icon-comment"></i>
                <span>{{post.comments}} comments</span>
              </span>
            </div> -->
          </div>
        </li>
      </ul>

      <!-- 分页组件 -->
      <div class="pagination" v-if="postList.length > 0">
        <button
          class="page-btn"
          :disabled="page <= 1"
          @click="changePage(page - 1)"
        >
          上一页
        </button>
        <span class="page-info">第 {{page}} 页</span>
        <button
          class="page-btn"
          @click="changePage(page + 1)"
        >
          下一页
        </button>
      </div>
    </div>
    <div class="right">
      <div class="communities">
        <h2 class="r-c-title">今日火热频道排行榜</h2>
        <ul class="r-c-content">
          <li class="r-c-item">
            <span class="index">1</span>
            <i class="icon"></i>
            b/coding
          </li>
          <li class="r-c-item">
            <span class="index">2</span>
            <i class="icon"></i>
            b/tree_hole
          </li>
          <li class="r-c-item">
            <span class="index">3</span>
            <i class="icon"></i>
            b/job
          </li>
        </ul>
        <button class="view-all">查看所有</button>
      </div>
      <div class="r-trending">
        <h2 class="r-t-title">持续热门频道</h2>
        <ul class="rank">
          <li class="r-t-cell">
            <div class="r-t-cell-info">
              <div class="avatar"></div>
              <div class="info">
                <span class="info-title">b/Book</span>
                <p class="info-num">7.1k members</p>
              </div>
            </div>
            <button class="join-btn">JOIN</button>
          </li>
          <li class="r-t-cell">
            <div class="r-t-cell-info">
              <div class="avatar"></div>
              <div class="info">
                <span class="info-title">b/coding</span>
                <p class="info-num">3.2k members</p>
              </div>
            </div>
            <button class="join-btn">JOIN</button>
          </li>
          <li class="r-t-cell">
            <div class="r-t-cell-info">
              <div class="avatar"></div>
              <div class="info">
                <span class="info-title">b/job</span>
                <p class="info-num">2.5k members</p>
              </div>
            </div>
            <button class="join-btn">JOIN</button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src

export default {
  name: "Home",
  components: {},
  data() {
    return {
      order: "time",
      page: 1,
      size: 10,
      postList: [],
      loading: false
    };
  },
  methods: {
    selectOrder(order){
      this.order = order;
      this.page = 1; // 切换排序时重置到第一页
      this.getPostList()
    },
    goPublish(){
      this.$router.push({ name: "Publish" });
    },
    goDetail(id){
      this.$router.push({ name: "Content", params: { id: id }});
    },
    changePage(newPage) {
      if (newPage < 1) return;
      this.page = newPage;
      this.getPostList();
    },
    getPostList() {
      if (this.loading) return;

      this.loading = true;
      this.$axios({
        method: "get",
        url: "/posts2",
        params: {
          page: this.page,
          size: this.size,
          order: this.order,
        }
      })
        .then(response => {
          console.log('帖子列表响应:', response.data);
          if (response.code == 1000) {
            this.postList = response.data || [];
          } else {
            console.log('获取帖子列表失败:', response.msg);
            this.postList = [];
          }
        })
        .catch(error => {
          console.log('请求错误:', error);
          this.postList = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    vote(post_id, direction){
      // 检查是否已登录
      const loginResult = localStorage.getItem('loginResult');
      console.log('检查登录状态:', loginResult ? '已登录' : '未登录');

      if (!loginResult) {
        alert('请先登录');
        this.$router.push('/login');
        return;
      }

      // 解析登录结果获取 token
      let token;
      try {
        const loginData = JSON.parse(loginResult);
        token = loginData.token;
        console.log('获取到token:', token ? '有效' : '无效');

        if (!token) {
          alert('登录状态异常，请重新登录');
          this.$router.push('/login');
          return;
        }
      } catch (e) {
        console.error('解析登录数据失败:', e);
        alert('登录状态异常，请重新登录');
        this.$router.push('/login');
        return;
      }

      // 找到对应的帖子
      const post = this.postList.find(p => p.post_id === post_id);
      if (!post) return;

      // 防止重复点击
      if (post.voting) return;

      // 如果点击的是当前状态，则取消投票
      const oldDirection = post.user_vote || 0;
      const newDirection = oldDirection === direction ? 0 : direction;

      // 设置加载状态
      this.$set(post, 'voting', true);

      // 乐观更新UI（先更新界面，如果失败再回滚）
      const oldVoteNum = post.vote_num || 0;
      post.user_vote = newDirection;

      // 计算新的投票数
      let voteChange = 0;
      if (oldDirection === 0 && newDirection !== 0) {
        // 从未投票到投票
        voteChange = newDirection;
      } else if (oldDirection !== 0 && newDirection === 0) {
        // 从投票到取消投票
        voteChange = -oldDirection;
      } else if (oldDirection !== 0 && newDirection !== 0) {
        // 从一种投票切换到另一种
        voteChange = newDirection - oldDirection;
      }

      post.vote_num = oldVoteNum + voteChange;

      console.log('发送投票请求:', {
        post_id: post_id,
        direction: newDirection,
        oldDirection: oldDirection,
        oldVoteNum: oldVoteNum
      });

      this.$axios({
        method: "post",
        url: "/vote",
        data: {
          post_id: post_id,
          direction: newDirection,
        }
      })
        .then(response => {
          console.log('投票响应完整数据:', response);
          console.log('投票响应 code:', response.code);

          if (response.code == 1000) {
            // 使用服务器返回的准确数据
            console.log('服务器返回的投票数据:', response.data);

            if (response.data) {
              post.vote_num = response.data.vote_num;
              post.user_vote = response.data.user_vote;
            }

            // 显示成功消息
            if (newDirection === 1) {
              console.log('👍 点赞成功');
            } else if (newDirection === -1) {
              console.log('👎 点踩成功');
            } else {
              console.log('✅ 取消投票');
            }
          } else {
            console.log('投票失败，响应:', response);
            // 回滚UI状态
            post.user_vote = oldDirection;
            post.vote_num = oldVoteNum;
            alert('投票失败: ' + (response.message || '未知错误'));
          }
        })
        .catch(error => {
          console.log('投票错误:', error);
          // 回滚UI状态
          post.user_vote = oldDirection;
          post.vote_num = oldVoteNum;
          alert('网络错误，请重试');
        })
        .finally(() => {
          // 清除加载状态
          this.$set(post, 'voting', false);
        });
    }
  },
  mounted: function() {
    this.getPostList();
  },
  computed:{
    timeOrder(){
      return this.order == "time";
    },
    scoreOrder(){
      return this.order == "score";
    }
  }
};
</script>

<style scoped lang="less">
.content {
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 48px auto 0;
  padding: 20px 24px;
  .left {
    width: 640px;
    padding-bottom: 10px;
    .c-l-title {
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      color: #1a1a1b;
      text-transform: unset;
      padding-bottom: 10px;
    }
    .c-l-header {
      align-items: center;
      background-color: #ffffff;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-flow: row nowrap;
      flex-flow: row nowrap;
      height: 56px;
      -ms-flex-pack: start;
      justify-content: flex-start;
      margin-bottom: 16px;
      padding: 0 12px;
      .iconfont {
        margin-right: 4px;
      }
      .btn-iconfont {
        display: flex;
        display: -webkit-flex;
      }
      .active {
        background: #f6f7f8;
        color: #0079d3;
        fill: #0079d3;
        border-radius: 20px;
        height: 32px;
        line-height: 32px;
        margin-right: 8px;
        padding: 0 10px;
      }
      .new {
        font-size: 14px;
        margin-right: 18px;
      }
      .top {
        font-size: 14px;
      }
      .btn-publish {
        width: 64px;
        height: 32px;
        line-height: 32px;
        background-color: #54b351;
        color: #ffffff;
        border: 1px solid transparent;
        border-radius: 4px;
        box-sizing: border-box;
        text-align: center;
        margin-left: auto;
        cursor: pointer;
      }
      .sort {
        margin-left: 300px;
        display: flex;
        color: #0079d3;
        display: -webkit-flex;
        align-items: center;
        .sort-triangle {
          width: 0;
          height: 0;
          border-top: 5px solid #0079d3;
          border-right: 5px solid transparent;
          border-bottom: 5px solid transparent;
          border-left: 5px solid transparent;
          margin-top: 5px;
          margin-left: 10px;
        }
      }
    }
    .c-l-list {
      .c-l-item {
        list-style: none;
        border-radius: 4px;
        padding-left: 40px;
        cursor: pointer;
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background-color: rgba(255, 255, 255, 0.8);
        color: #878a8c;
        position: relative;
        .post {
          align-items: center;
          box-sizing: border-box;
          display: -ms-flexbox;
          display: flex;
          -ms-flex-direction: column;
          flex-direction: column;
          height: 100%;
          left: 0;
          padding: 8px 4px 8px 0;
          position: absolute;
          top: 0;
          width: 40px;
          border-left: 4px solid transparent;
          background: #f8f9fa;

          .vote {
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            margin: 2px 0;

            &:hover {
              background-color: #e9ecef;
              transform: scale(1.1);
            }

            &:active {
              transform: scale(0.95);
            }

            &.active {
              color: #ff4500;
              background-color: #fff2e6;
              border: 1px solid #ff4500;

              &:hover {
                background-color: #ffe0cc;
                color: #cc3700;
              }
            }

            &.upvote.active {
              color: #ff4500;
            }

            &.downvote.active {
              color: #7193ff;
            }

            &.loading {
              pointer-events: none;
              opacity: 0.6;
            }
          }

          .loading-spinner {
            animation: spin 1s linear infinite;
            font-size: 14px;
          }

          .iconfont {
            margin-right: 0;
            font-size: 16px;
            transition: all 0.2s ease;
          }

          .text {
            color: #1a1a1b;
            font-size: 12px;
            font-weight: 700;
            line-height: 16px;
            pointer-events: none;
            word-break: normal;
            margin: 4px 0;
            transition: color 0.2s ease;

            &.positive {
              color: #ff4500;
            }

            &.negative {
              color: #7193ff;
            }
          }
        }
        .l-container {
          padding: 15px;
          .con-title {
            color: #000000;
            font-size: 18px;
            font-weight: 500;
            line-height: 22px;
            text-decoration: none;
            word-break: break-word;
          }
          .con-memo {
            margin-top: 10px;
            margin-bottom: 10px;
          }
          .post-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            .author, .community {
              margin-right: 15px;
            }
          }
          .con-cover {
            height: 512px;
            width: 100%;
            background: url("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1585999647247&di=7e9061211c23e3ed9f0c4375bb3822dc&imgtype=0&src=http%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F04d8cda08e170f4a58c18c45a93c539375c22162.jpg")
              no-repeat;
            background-size: cover;
            margin-top: 10px;
            margin-bottom: 10px;
          }
          .user-btn {
            font-size: 14px;
            display: flex;
            display: -webkit-flex;
            .btn-item {
              display: flex;
              display: -webkit-flex;
              margin-right: 10px;
              .iconfont {
                margin-right: 4px;
              }
            }
          }
        }
      }
    }

    // 分页样式
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
      padding: 20px 0;

      .page-btn {
        padding: 8px 16px;
        margin: 0 10px;
        background-color: #0079d3;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover:not(:disabled) {
          background-color: #005ba1;
        }

        &:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }

      .page-info {
        margin: 0 15px;
        font-size: 14px;
        color: #666;
      }
    }
  }

  // 旋转动画
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .right {
    width: 312px;
    margin-left: 24px;
    margin-top: 28px;
    .communities {
      background-color: #ffffff;
      color: #1a1a1b;
      border: 1px solid #ccc;
      border-radius: 4px;
      overflow: visible;
      word-wrap: break-word;
      margin-bottom: 20px;
      .r-c-title {
        background-image: linear-gradient(
          0deg,
          rgba(0, 0, 0, 0.3) 0,
          transparent
        );
        background-color: #0079d3;
        height: 80px;
        width: 100%;
        color: #fff;
        font-size: 20px;
        line-height: 120px;
        padding-left: 10px;
        box-sizing: border-box;
        text-align: center;
      }
      .r-c-content {
        .r-c-item {
          align-items: center;
          display: flex;
          display: -webkit-flex;
          height: 48px;
          padding: 0 12px;
          border-bottom: thin solid #edeff1;
          font-size: 14px;
          .index {
            width: 20px;
            color: #1c1c1c;
            font-size: 14px;
            font-weight: 500;
            line-height: 18px;
          }
          .icon {
            width: 32px;
            height: 32px;
            background-image: url("../assets/images/avatar.png");
            background-repeat: no-repeat;
            background-size: cover;
            margin-right: 20px;
          }
          &:last-child {
            border-bottom: none;
          }
        }
      }
      .view-all {
        background-color: #0079d3;
        border: 1px solid transparent;
        border-radius: 4px;
        box-sizing: border-box;
        text-align: center;
        letter-spacing: 1px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 700;
        letter-spacing: 0.5px;
        line-height: 24px;
        text-transform: uppercase;
        padding: 3px 0;
        width: 280px;
        color: #fff;
        margin: 20px 0 20px 16px;
      }
    }
    .r-trending {
      padding-top: 16px;
      width: 312px;
      background-color: #ffffff;
      color: #1a1a1b;
      fill: #1a1a1b;
      border: 1px solid #cccccc;
      border-radius: 4px;
      overflow: visible;
      word-wrap: break-word;
      .r-t-title {
        font-size: 10px;
        font-weight: 700;
        letter-spacing: 0.5px;
        line-height: 12px;
        text-transform: uppercase;
        background-color: #ffffff;
        border-radius: 3px 3px 0 0;
        color: #1a1a1b;
        display: -ms-flexbox;
        display: flex;
        fill: #1a1a1b;
        padding: 0 12px 12px;
      }
      .rank {
        padding: 12px;
        .r-t-cell {
          display: flex;
          display: -webkit-flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          .r-t-cell-info {
            display: flex;
          }
          .avatar {
            width: 32px;
            height: 32px;
            background: url("../assets/images/avatar.png") no-repeat;
            background-size: cover;
            margin-right: 10px;
          }
          .info {
            margin-right: 10px;
            .info-title {
              font-size: 12px;
              font-weight: 500;
              line-height: 16px;
              text-overflow: ellipsis;
              width: 144px;
            }
            .info-num {
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              padding-bottom: 4px;
            }
          }
          .join-btn {
            width: 106px;
            height: 32px;
            line-height: 32px;
            background-color: #0079d3;
            color: #ffffff;
            border: 1px solid transparent;
            border-radius: 4px;
            box-sizing: border-box;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
