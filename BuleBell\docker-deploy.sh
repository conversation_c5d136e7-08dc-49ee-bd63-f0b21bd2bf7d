#!/bin/bash

# BlueBell项目Docker部署脚本
# 使用方法: ./docker-deploy.sh [start|stop|restart|logs|clean]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="bluebell"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 启动服务
start_services() {
    print_message $GREEN "正在启动BlueBell服务..."
    
    # 构建并启动服务
    docker-compose up -d --build
    
    print_message $GREEN "服务启动完成！"
    print_message $YELLOW "Web应用: http://localhost:8082"
    print_message $YELLOW "前端应用: http://localhost:80"
    print_message $YELLOW "MySQL: localhost:3306"
    print_message $YELLOW "Redis: localhost:6379"
}

# 停止服务
stop_services() {
    print_message $YELLOW "正在停止BlueBell服务..."
    docker-compose down
    print_message $GREEN "服务已停止"
}

# 重启服务
restart_services() {
    print_message $YELLOW "正在重启BlueBell服务..."
    docker-compose down
    docker-compose up -d --build
    print_message $GREEN "服务重启完成！"
}

# 查看日志
show_logs() {
    print_message $GREEN "显示服务日志..."
    docker-compose logs -f
}

# 清理资源
clean_resources() {
    print_message $YELLOW "正在清理Docker资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像
    docker images | grep $PROJECT_NAME | awk '{print $3}' | xargs -r docker rmi -f
    
    # 清理未使用的资源
    docker system prune -f
    
    print_message $GREEN "清理完成"
}

# 显示服务状态
show_status() {
    print_message $GREEN "服务状态:"
    docker-compose ps
}

# 显示帮助信息
show_help() {
    echo "BlueBell Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start    - 启动所有服务"
    echo "  stop     - 停止所有服务"
    echo "  restart  - 重启所有服务"
    echo "  logs     - 查看服务日志"
    echo "  status   - 显示服务状态"
    echo "  clean    - 清理所有Docker资源"
    echo "  help     - 显示此帮助信息"
    echo ""
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        clean)
            read -p "确定要清理所有Docker资源吗？(y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                clean_resources
            else
                print_message $YELLOW "取消清理操作"
            fi
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
