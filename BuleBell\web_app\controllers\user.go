package controllers

import (
	"net/http"
	"strings"
	"web_app/logic"
	"web_app/models"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// SignUpHandler 用户注册接口
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 用户相关接口
// @Accept json
// @Produce json
// @Param object body models.ParamSignUp true "注册参数"
// @Success 200 {object} controllers.ResponseData
// @Router /signup [post]
func SignUpHandler(c *gin.Context) {
	//1.获取参数并校验
	p := new(models.ParamSignUp)
	if err := c.ShouldBindJSON(&p); err != nil {
		//请求参数有误，直接返回响应
		zap.L().Error("SignUp with invalid param", zap.Error(err))
		//判断err是否为validator.ValidationErrors类型
		if errs, ok := err.(validator.ValidationErrors); ok {
			//翻译错误
			errData := make(map[string]string)
			for _, e := range errs {
				errData[e.Field()] = e.Translate(trans)
			}
			// 使用ResponseData结构体直接返回，包含验证错误详情
			c.JSON(http.StatusOK, &ResponseData{
				Code:    CodeInvalidParam,
				Message: GetMsg(CodeInvalidParam),
				Data:    errData,
			})
			return
		}
		ResponseError(c, CodeInvalidParam, err.Error())
		return
	}
	//2.业务处理
	err := logic.SignUp(p)
	if err != nil {
		zap.L().Error("logic.SignUp failed", zap.Error(err))
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "用户已存在") {
			ResponseError(c, CodeUserExist, GetMsg(CodeUserExist))
			return
		}
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	//3.返回响应
	ResponseSuccess(c, nil)
}

// LoginHandler 用户登录接口
// @Summary 用户登录
// @Description 用户登录接口
// @Tags 用户相关接口
// @Accept json
// @Produce json
// @Param object body models.ParamLogin true "登录参数"
// @Success 200 {object} controllers.ResponseData
// @Router /login [post]
func LoginHandler(c *gin.Context) {
	//1.获取参数并校验
	p := new(models.ParamLogin)
	if err := c.ShouldBindJSON(&p); err != nil {
		//请求参数有误，直接返回响应
		zap.L().Error("Login with invalid param", zap.Error(err))
		//判断err是否为validator.ValidationErrors类型
		if errs, ok := err.(validator.ValidationErrors); ok {
			//翻译错误
			errData := make(map[string]string)
			for _, e := range errs {
				errData[e.Field()] = e.Translate(trans)
			}
			// 使用ResponseData结构体直接返回，包含验证错误详情
			c.JSON(http.StatusOK, &ResponseData{
				Code:    CodeInvalidParam,
				Message: GetMsg(CodeInvalidParam),
				Data:    errData,
			})
			return
		}
		ResponseError(c, CodeInvalidParam, err.Error())
		return
	}
	//2.业务处理
	token, err := logic.Login(p)
	if err != nil {
		zap.L().Error("logic.Login failed", zap.Error(err))
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "用户不存在") {
			ResponseError(c, CodeUserNotExist, GetMsg(CodeUserNotExist))
			return
		}
		if strings.Contains(err.Error(), "密码错误") {
			ResponseError(c, CodeInvalidPassword, GetMsg(CodeInvalidPassword))
			return
		}
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	//3.返回响应，包含token
	ResponseSuccess(c, gin.H{
		"token": token,
	})
}
