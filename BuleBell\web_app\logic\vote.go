package logic

import (
	"time"
	"web_app/dao/mysql"
	"web_app/dao/redis"
	"web_app/models"

	"go.uber.org/zap"
)

// VoteForPost 用户为帖子投票
func VoteForPost(userID, postID int64, direction int8) (*models.VoteData, error) {
	// 1. 检查帖子是否存在
	_, err := mysql.GetPostDetailByID(postID)
	if err != nil {
		zap.L().Error("获取帖子信息失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return nil, err
	}

	// 2. 获取帖子创建时间（用于评分计算）
	createTime := time.Now().Unix() // 简化实现，实际应该从数据库获取

	// 3. Redis 投票操作
	err = redis.VoteForPost(postID, userID, direction, createTime)
	if err != nil {
		zap.L().Error("Redis投票操作失败",
			zap.Int64("user_id", userID),
			zap.Int64("post_id", postID),
			zap.Int8("direction", direction),
			zap.Error(err))
		return nil, err
	}

	// 4. 异步更新 MySQL（可选，确保数据一致性）
	go func() {
		// 这里可以实现 MySQL 投票记录的更新
		// 暂时省略，主要依赖 Redis
	}()

	// 5. 获取最新的投票统计
	voteInfo, err := redis.GetPostVoteInfo(postID)
	if err != nil {
		zap.L().Error("获取投票统计失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return nil, err
	}

	// 6. 获取用户最新的投票状态
	userVote, err := redis.GetUserVote(userID, postID)
	if err != nil {
		zap.L().Warn("获取用户投票状态失败", zap.Error(err))
		userVote = direction // 使用传入的方向作为备选
	}

	// 7. 返回投票结果
	voteData := &models.VoteData{
		VoteNum:  voteInfo.VoteNum,
		UserVote: userVote,
	}

	return voteData, nil
}

// GetPostVoteInfo 获取帖子投票信息
func GetPostVoteInfo(postID, userID int64) (*models.VoteData, error) {
	// 1. 获取投票统计
	voteInfo, err := redis.GetPostVoteInfo(postID)
	if err != nil {
		zap.L().Error("获取投票统计失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return nil, err
	}

	// 2. 获取用户投票状态
	var userVote int8 = 0
	if userID > 0 {
		userVote, err = redis.GetUserVote(userID, postID)
		if err != nil {
			zap.L().Warn("获取用户投票状态失败",
				zap.Int64("user_id", userID),
				zap.Int64("post_id", postID),
				zap.Error(err))
			userVote = 0 // 默认未投票
		}
	}

	// 3. 返回结果
	voteData := &models.VoteData{
		VoteNum:  voteInfo.VoteNum,
		UserVote: userVote,
	}

	return voteData, nil
}
