-- 初始化数据库脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS web_app DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE web_app;

-- 创建用户表（示例）
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(128) NOT NULL COMMENT '密码',
    email VARCHAR(128) COMMENT '邮箱',
    gender TINYINT NOT NULL DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建社区表（示例）
CREATE TABLE IF NOT EXISTS communities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    community_id BIGINT NOT NULL UNIQUE COMMENT '社区ID',
    community_name VARCHAR(128) NOT NULL UNIQUE COMMENT '社区名称',
    introduction TEXT COMMENT '社区介绍',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社区表';

-- 创建帖子表（示例）
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    post_id BIGINT NOT NULL UNIQUE COMMENT '帖子ID',
    title VARCHAR(128) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    community_id BIGINT NOT NULL COMMENT '社区ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '帖子状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_author_id (author_id),
    INDEX idx_community_id (community_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子表';

-- 插入一些示例数据
INSERT INTO communities (community_id, community_name, introduction) VALUES
(1, 'Go语言', 'Go语言学习交流社区'),
(2, 'Vue.js', 'Vue.js前端开发社区'),
(3, '数据库', '数据库技术讨论社区');

-- 创建管理员用户（密码需要在应用中加密）
INSERT INTO users (username, password, email, gender) VALUES
('admin', '$2a$10$example_hashed_password', '<EMAIL>', 0);

-- 设置权限
GRANT ALL PRIVILEGES ON web_app.* TO 'root'@'%';
FLUSH PRIVILEGES;
