# 使用Ubuntu作为基础镜像（通常下载更稳定）
FROM ubuntu:20.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装必要的包
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Go
RUN wget https://golang.org/dl/go1.21.0.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz && \
    rm go1.21.0.linux-amd64.tar.gz

# 设置Go环境变量
ENV PATH=$PATH:/usr/local/go/bin
ENV GOPATH=/go
ENV GOPROXY=https://goproxy.cn,direct

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY web_app/go.mod web_app/go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY web_app/ ./

# 整理依赖并构建应用
RUN go mod tidy && go build -o main .

# 创建非root用户
RUN useradd -m -u 1001 appuser && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8082

# 启动应用
CMD ["./main", "-c", "conf/config.docker.yaml"]
