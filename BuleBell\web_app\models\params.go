package models

//定义请求的参数结构体

type ParamSignUp struct {
	Username   string `json:"username" binding:"required" label:"用户名" msg:"用户名不能为空"`
	Password   string `json:"password" binding:"required" label:"密码" msg:"密码不能为空"`
	RePassword string `json:"re_password" binding:"required,eqfield=Password" label:"确认密码" msg:"两次密码不一致"`
}

type ParamLogin struct {
	Username string `json:"username" binding:"required" label:"用户名" msg:"用户名不能为空"`
	Password string `json:"password" binding:"required" label:"密码" msg:"密码不能为空"`
}

// ParamVote 投票参数
type ParamVote struct {
	PostID    int64 `json:"post_id" binding:"required"`       // 帖子ID
	Direction int8  `json:"direction" binding:"oneof=-1 0 1"` // 投票方向：1=赞成，-1=反对，0=取消投票
}

// VoteData 投票响应数据
type VoteData struct {
	VoteNum  int64 `json:"vote_num"`  // 投票总数（赞成票-反对票）
	UserVote int8  `json:"user_vote"` // 当前用户的投票状态：1=已赞成，-1=已反对，0=未投票
}

// PostVoteInfo 帖子投票统计信息
type PostVoteInfo struct {
	PostID     int64   `json:"post_id" db:"post_id"`         // 帖子ID
	UpVotes    int64   `json:"up_votes" db:"up_votes"`       // 赞成票数
	DownVotes  int64   `json:"down_votes" db:"down_votes"`   // 反对票数
	VoteNum    int64   `json:"vote_num" db:"vote_num"`       // 净投票数（赞成-反对）
	Score      float64 `json:"score" db:"score"`             // 帖子评分
	CreateTime int64   `json:"create_time" db:"create_time"` // 创建时间戳
}
