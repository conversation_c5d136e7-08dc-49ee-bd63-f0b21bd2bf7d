package snowflake

import (
	"time"

	sf "github.com/bwmarrin/snowflake"
)

// node 是全局的 Snowflake 节点实例
var node *sf.Node

// Init 初始化 Snowflake 节点
// startTime: 起始时间（格式为 "2006-01-02"）
// machineID: 机器ID（必须唯一）
// 返回 error，如果初始化失败
func Init(startTime string, machineID int64) (err error) {
	var st time.Time
	// 解析起始时间字符串
	st, err = time.Parse("2006-01-02", startTime)
	if err != nil {
		return
	}
	// 设置 Snowflake 的起始时间（毫秒级时间戳）
	sf.Epoch = st.UnixNano() / 1000000
	// 创建新的节点实例
	node, err = sf.NewNode(machineID)
	return
}

// GenID 生成唯一ID
func GenID() int64 {
	return node.Generate().Int64()
}
