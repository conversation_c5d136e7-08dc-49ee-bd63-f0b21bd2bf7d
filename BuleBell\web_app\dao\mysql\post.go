package mysql

import (
	"fmt"
	"web_app/models"
)

func CreatePost(post *models.Post) error {
	sqlStr := `insert into post (post_id, title, content,author_id,community_id) values (?, ?, ?, ?, ?)`
	_, err := db.Exec(sqlStr, post.PostID, post.Title, post.Content, post.AuthorID, post.CommunityID)
	if err != nil {
		return err
	}
	return nil
}

func GetPostDetailByID(postID int64) (post *models.ApiPostDetail, err error) {
	// 查询帖子详情，包含投票信息
	sqlStr := `
		SELECT p.post_id, p.title, p.content, p.author_id, p.community_id,
		       COALESCE(p.vote_num, 0) as vote_num,
		       COALESCE(p.score, 0) as score,
		       COALESCE(u.username, '未知用户') as author_name,
		       COALESCE(c.community_name, '未知社区') as community_name
		FROM post p
		LEFT JOIN user u ON p.author_id = u.user_id
		LEFT JOIN community c ON p.community_id = c.community_id
		WHERE p.post_id = ?`

	// 创建一个实例来接收查询结果
	post = new(models.ApiPostDetail)

	// 添加调试日志
	fmt.Printf("查询帖子详情，ID: %d\n", postID)

	err = db.Get(post, sqlStr, postID)
	if err != nil {
		fmt.Printf("数据库查询错误: %v\n", err)
		return nil, err
	}

	fmt.Printf("查询成功，帖子信息: %+v\n", post)
	return post, nil
}

// GetPostList 获取帖子列表，支持分页和排序
func GetPostList(page, size int64, order string) ([]*models.ApiPostDetail, error) {
	// 计算偏移量
	offset := (page - 1) * size

	// 根据排序参数构建 ORDER BY 子句
	var orderBy string
	switch order {
	case "time":
		orderBy = "create_time DESC"
	case "score":
		orderBy = "create_time DESC" // 暂时用时间排序，后续可以加入评分逻辑
	default:
		orderBy = "create_time DESC"
	}

	sqlStr := fmt.Sprintf(`
		SELECT p.post_id, p.title, p.content, p.author_id, p.community_id,
		       COALESCE(p.vote_num, 0) as vote_num,
		       COALESCE(p.score, 0) as score,
		       COALESCE(u.username, '未知用户') as author_name,
		       COALESCE(c.community_name, '未知社区') as community_name
		FROM post p
		LEFT JOIN user u ON p.author_id = u.user_id
		LEFT JOIN community c ON p.community_id = c.community_id
		WHERE p.status = 1 OR p.status IS NULL
		ORDER BY p.%s
		LIMIT ? OFFSET ?`, orderBy)

	posts := make([]*models.ApiPostDetail, 0)
	err := db.Select(&posts, sqlStr, size, offset)
	if err != nil {
		fmt.Printf("GetPostList 数据库查询错误: %v\n", err)
		return nil, err
	}

	fmt.Printf("GetPostList 查询成功，返回 %d 条记录\n", len(posts))
	return posts, nil
}

// GetUserByID 根据用户ID获取用户信息
func GetUserByID(userID int64) (*models.User, error) {
	user := new(models.User)
	sqlStr := `select user_id, username from user where user_id = ?`
	err := db.Get(user, sqlStr, userID)
	if err != nil {
		return nil, err
	}
	return user, nil
}
