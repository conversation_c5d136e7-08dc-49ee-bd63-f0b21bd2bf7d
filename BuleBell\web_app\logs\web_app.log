{"level":"DEBUG","time":"2025-08-21T00:50:03.958+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:03.967+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:03.970+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"ERROR","time":"2025-08-21T00:50:03.975+0800","caller":"redis/redis.go:73","msg":"Redis 连接失败","error":"ERR Client sent AUTH, but no password is set"}
{"level":"INFO","time":"2025-08-21T00:50:03.976+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:50:39.263+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:39.270+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:39.272+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:56:47.078+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:56:47.086+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:56:47.087+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:57:03.971+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:57:11.973+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:57:11.982+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:57:11.984+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:58.820+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:57:59.083+0800","caller":"logger/logger.go:81","msg":"/favicon.ico","status":404,"method":"GET","path":"/favicon.ico","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:16.267+0800","caller":"logger/logger.go:81","msg":"/","status":404,"method":"GET","path":"/","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:28.264+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:04:33.668+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:04:33.676+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:04:33.678+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:04:40.377+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:04:43.268+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:06:05.362+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:06:05.369+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:06:05.371+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:14:42.857+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:14:42.866+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:14:42.867+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:27:35.532+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:27:35.542+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:30:52.312+0800","caller":"web_app/main.go:37","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T01:30:52.321+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"web_app/main.go:64","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:75","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:86","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:30:59.246+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:30:28.157+0800","caller":"web_app/main.go:41","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:30:28.185+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"web_app/main.go:68","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:79","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:90","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:33:00.720+0800","caller":"web_app/main.go:40","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:33:00.731+0800","caller":"web_app/main.go:67","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:78","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:89","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:33:14.221+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T20:57:39.329+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T20:57:39.340+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:00:21.288+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:00:30.736+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:01:12.836+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:03.238+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:09.861+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:53.980+0800","caller":"logger/logger.go:80","msg":"/paramsignup","status":404,"method":"GET","path":"/paramsignup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:57.881+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-21T21:04:10.980+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:04:10.992+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:04:26.156+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '\"' after object key:value pair"}
{"level":"INFO","time":"2025-08-21T21:04:26.157+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:05:11.847+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:06:29.480+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:06:29.489+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:06:33.582+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:12:13.762+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:12:13.771+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:12:22.100+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006566}
{"level":"ERROR","time":"2025-08-21T21:12:30.834+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:12:30.834+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:22:39.786+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:22:48.751+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005049}
{"level":"ERROR","time":"2025-08-21T21:22:54.091+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:22:54.091+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006601}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:24:11.245+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:25:01.483+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:25:01.492+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:25:01.493+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:25:01.493+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:27:41.093+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:27:41.102+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:27:41.129+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:27:41.129+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:27:50.952+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:27:50.953+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010038}
{"level":"INFO","time":"2025-08-21T21:28:23.110+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:38:40.127+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:38:56.526+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:38:56.536+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:38:56.537+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:38:56.537+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:39:11.834+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:39:11.834+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:39:22.855+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"json: cannot unmarshal number into Go struct field ParamSignUp.re_password of type string"}
{"level":"INFO","time":"2025-08-21T21:39:22.855+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:40:02.046+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:40:02.046+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:40:02.047+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:40:02.047+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:40:04.923+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:40:04.931+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:40:04.932+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:40:04.932+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:40:10.277+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:15.556+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:15.556+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:41.073+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:41.073+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:49.981+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.RePassword' Error:Field validation for 'RePassword' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:40:49.981+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:56.876+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:56.876+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:41:00.758+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.RePassword' Error:Field validation for 'RePassword' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:41:00.758+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:43:59.999+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:44:00.008+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:44:00.009+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:44:00.009+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:44:04.952+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:44:04.952+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:46:44.443+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:44.443+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010003}
{"level":"INFO","time":"2025-08-21T21:46:50.862+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:46:50.862+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:46:50.863+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:46:50.863+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:46:53.001+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:46:53.010+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:46:53.010+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:46:53.011+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:46:57.793+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:57.794+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006399}
{"level":"ERROR","time":"2025-08-21T21:46:58.710+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:58.710+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:47:13.940+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:48:05.019+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T21:48:05.019+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:48:23.275+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:49:53.037+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T21:49:53.037+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T23:00:31.548+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T23:31:17.991+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T23:31:18.002+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T23:31:18.003+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T23:31:18.003+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T23:31:37.003+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T23:31:37.003+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T23:31:51.244+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0153443}
{"level":"ERROR","time":"2025-08-21T23:32:06.818+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-21T23:32:06.818+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005187}
{"level":"INFO","time":"2025-08-21T23:58:35.344+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T23:58:35.344+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T23:58:35.345+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T23:58:35.345+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T15:25:02.904+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T15:25:02.917+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T15:25:02.918+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T15:25:02.919+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T15:25:29.744+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-22T15:25:29.744+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0018587}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:18:43.903+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:18:43.915+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:18:43.916+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:18:43.916+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T17:18:54.330+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.password' Error:Field validation for 'password' failed on the 'required' tag\nKey: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-22T17:18:54.331+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.001723}
{"level":"INFO","time":"2025-08-22T17:19:05.625+0800","caller":"logger/logger.go:94","msg":"/login","status":404,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:19:07.771+0800","caller":"logger/logger.go:94","msg":"/login","status":404,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:19:39.659+0800","caller":"logger/logger.go:94","msg":"/login/","status":404,"method":"POST","path":"/login/","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:20:48.848+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:20:48.858+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:20:48.858+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:20:48.859+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T17:21:04.122+0800","caller":"controllers/user.go:56","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-22T17:21:04.122+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.000681}
{"level":"ERROR","time":"2025-08-22T17:21:17.437+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:17.437+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0016917}
{"level":"ERROR","time":"2025-08-22T17:21:36.826+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:36.826+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005063}
{"level":"ERROR","time":"2025-08-22T17:21:51.948+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:51.949+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005093}
{"level":"ERROR","time":"2025-08-22T17:22:06.586+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-22T17:22:06.586+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005045}
{"level":"INFO","time":"2025-08-22T17:22:11.311+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0115282}
{"level":"ERROR","time":"2025-08-22T17:22:28.567+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:22:28.567+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.000537}
{"level":"INFO","time":"2025-08-22T17:23:20.750+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:27:39.578+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:27:39.595+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:27:39.596+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:27:39.597+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-22T17:28:11.179+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0083916}
{"level":"INFO","time":"2025-08-22T17:28:22.984+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0008358}
{"level":"INFO","time":"2025-08-22T18:03:08.266+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T23:10:02.706+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T23:10:02.718+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T23:10:02.718+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T23:10:02.719+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-22T23:10:41.151+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0019645}
{"level":"INFO","time":"2025-08-22T23:10:55.738+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T23:10:55.738+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T23:10:55.738+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T23:10:55.738+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T23:13:57.298+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T23:13:57.309+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T23:13:57.310+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T23:13:57.310+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-22T23:14:07.667+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010134}
{"level":"INFO","time":"2025-08-22T23:18:29.960+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T23:18:29.960+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T23:18:29.960+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T23:18:29.960+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-23T00:17:18.664+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-23T00:17:18.679+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-23T00:17:18.680+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-23T00:17:18.680+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-23T00:17:37.596+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-23T00:17:37.596+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-23T00:17:37.596+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-23T00:17:37.597+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T14:38:33.226+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T14:38:33.317+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T14:38:33.327+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T14:38:33.327+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T14:39:52.177+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:39:52.774+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:39:53.256+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:39:54.567+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:39:55.241+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:39:55.377+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:01.074+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:02.449+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:08.823+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:10.488+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:50.425+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:53.721+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:54.380+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:54.554+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:40:54.648+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:43:42.380+0800","caller":"logger/logger.go:94","msg":"/api/v1/ping","status":404,"method":"GET","path":"/api/v1/ping","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:43:48.633+0800","caller":"logger/logger.go:94","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:45:11.706+0800","caller":"logger/logger.go:94","msg":"/api/v1/ping","status":404,"method":"GET","path":"/api/v1/ping","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:45:19.736+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:45:26.757+0800","caller":"logger/logger.go:94","msg":"/posts2","status":404,"method":"GET","path":"/posts2","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:45:33.603+0800","caller":"logger/logger.go:94","msg":"/","status":404,"method":"GET","path":"/","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:46:51.401+0800","caller":"logger/logger.go:94","msg":"/api/v1/ping","status":404,"method":"GET","path":"/api/v1/ping","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:47:18.098+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":404,"method":"GET","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:47:22.933+0800","caller":"logger/logger.go:94","msg":"/login","status":404,"method":"GET","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:50:20.654+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T14:50:20.654+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T14:50:20.654+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T14:50:20.654+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T14:50:24.694+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T14:50:24.707+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T14:50:24.707+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T14:50:24.708+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T14:50:50.269+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:50:57.287+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.username' Error:Field validation for 'username' failed on the 'required' tag\nKey: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:50:57.287+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:50:58.075+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.username' Error:Field validation for 'username' failed on the 'required' tag\nKey: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:50:58.076+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005862}
{"level":"ERROR","time":"2025-08-24T14:50:58.528+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.username' Error:Field validation for 'username' failed on the 'required' tag\nKey: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:50:58.528+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:50:59.947+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:50:59.947+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005151}
{"level":"ERROR","time":"2025-08-24T14:51:00.840+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:51:00.841+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005596}
{"level":"ERROR","time":"2025-08-24T14:51:01.028+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:51:01.028+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:51:02.436+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T14:51:02.436+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0095927}
{"level":"ERROR","time":"2025-08-24T14:51:02.914+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T14:51:02.914+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0010825}
{"level":"INFO","time":"2025-08-24T14:51:10.681+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0013799}
{"level":"INFO","time":"2025-08-24T14:51:11.028+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T14:51:32.842+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005059}
{"level":"INFO","time":"2025-08-24T14:51:33.174+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:51:57.634+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:51:57.634+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:52:07.237+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.237+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:52:07.406+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.406+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005131}
{"level":"ERROR","time":"2025-08-24T14:52:07.500+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.500+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0006167}
{"level":"ERROR","time":"2025-08-24T14:52:07.577+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.577+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:52:07.767+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.767+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:52:07.921+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:07.921+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005063}
{"level":"ERROR","time":"2025-08-24T14:52:08.092+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:08.092+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T14:52:30.514+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.username' Error:Field validation for 'username' failed on the 'required' tag\nKey: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:52:30.514+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005049}
{"level":"ERROR","time":"2025-08-24T14:52:34.260+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T14:52:34.260+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005871}
{"level":"ERROR","time":"2025-08-24T14:52:35.815+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T14:52:35.815+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005231}
{"level":"INFO","time":"2025-08-24T14:54:34.137+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T14:54:34.144+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T14:54:34.144+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T14:54:34.144+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T14:57:02.401+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T14:57:02.422+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T14:57:02.424+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T14:57:02.425+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-24T14:57:13.859+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T14:57:13.860+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0027151}
{"level":"ERROR","time":"2025-08-24T14:57:21.346+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:57:21.346+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.00051}
{"level":"ERROR","time":"2025-08-24T14:57:22.189+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T14:57:22.190+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0009983}
{"level":"INFO","time":"2025-08-24T15:02:54.376+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.002394}
{"level":"INFO","time":"2025-08-24T15:02:54.632+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:06:59.746+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T15:06:59.747+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T15:06:59.747+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T15:06:59.747+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T15:07:04.243+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T15:07:04.253+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T15:07:04.254+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T15:07:04.255+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T15:07:18.917+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:07:25.854+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T15:07:35.342+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T15:07:35.342+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0052844}
{"level":"ERROR","time":"2025-08-24T15:07:35.585+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T15:07:35.585+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001618}
{"level":"ERROR","time":"2025-08-24T15:07:37.612+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T15:07:37.612+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005035}
{"level":"ERROR","time":"2025-08-24T15:07:38.350+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T15:07:38.350+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005054}
{"level":"INFO","time":"2025-08-24T15:08:07.441+0800","caller":"logger/logger.go:94","msg":"/api/v1/signup","status":200,"method":"POST","path":"/api/v1/signup","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0355623}
{"level":"ERROR","time":"2025-08-24T15:09:17.065+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.username' Error:Field validation for 'username' failed on the 'required' tag\nKey: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T15:09:17.065+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:28.080+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0021549}
{"level":"INFO","time":"2025-08-24T15:11:28.343+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:31.018+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:31.728+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:31.991+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:32.445+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:33.842+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":404,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T15:11:41.708+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:10:52.809+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T16:10:52.809+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T16:10:52.809+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T16:10:52.809+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T16:26:02.350+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T16:26:02.368+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T16:26:02.369+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T16:26:02.370+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T16:26:38.907+0800","caller":"logger/logger.go:94","msg":"/api/v1/ping","status":200,"method":"GET","path":"/api/v1/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:33:01.907+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T16:33:01.907+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T16:33:01.907+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T16:33:01.907+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T16:33:09.917+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T16:33:09.934+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T16:33:09.935+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T16:33:09.936+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T16:38:47.511+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T16:38:47.511+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T16:38:47.511+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T16:38:47.511+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T16:45:58.572+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T16:45:58.582+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T16:45:58.583+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T16:45:58.584+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T16:47:55.893+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T16:47:55.905+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T16:47:55.905+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T16:47:55.907+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T16:49:21.504+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:49:24.050+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":404,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:49:40.456+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:49:40.704+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:16.703+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:16.960+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:17.078+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:27.740+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:30.386+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:30.674+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:31.445+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:31.712+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T16:51:43.573+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":404,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T17:03:17.191+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:03:17.204+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:03:17.205+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:03:17.207+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:05:26.795+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:05:26.806+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:05:26.806+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:05:26.807+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:07:11.137+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:07:11.151+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:07:11.152+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:07:11.152+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:07:30.702+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:07:30.716+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:07:30.717+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:07:30.717+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:09:19.064+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:09:19.074+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:09:19.075+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:09:19.080+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:11:34.907+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:11:34.921+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:11:34.922+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:11:34.923+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:12:24.384+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:12:24.401+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:12:24.402+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:12:24.403+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:12:46.479+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:12:46.489+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:12:46.489+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:12:46.490+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:13:01.713+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:13:01.722+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:13:01.723+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:13:01.723+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:13:09.784+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:13:09.793+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:13:09.793+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:13:09.793+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:13:19.891+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:13:19.899+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:13:19.900+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:13:19.901+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:13:25.972+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:13:25.982+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:13:25.984+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:13:25.986+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:13:47.681+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:13:47.692+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:13:47.692+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:13:47.694+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:14:07.336+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:14:07.345+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:14:07.346+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:14:07.347+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:14:58.565+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:14:58.574+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:14:58.575+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:14:58.577+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T17:15:15.882+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T17:15:19.738+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T17:15:21.315+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T17:15:36.414+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T17:16:57.431+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:16:57.441+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:16:57.441+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:16:57.442+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:19:18.779+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:19:18.790+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:19:18.791+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:19:18.791+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T17:22:13.451+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T17:22:13.469+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T17:22:13.470+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T17:22:13.471+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T17:22:13.501+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0023925}
{"level":"INFO","time":"2025-08-24T17:33:02.347+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T17:33:02.347+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T17:33:02.347+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T17:33:02.347+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T18:02:15.112+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:02:15.131+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:02:15.132+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:02:15.133+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T18:02:17.586+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0028134}
{"level":"INFO","time":"2025-08-24T18:02:21.551+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:02:28.347+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:02:28.755+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:02:29.324+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:02:30.263+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:08:22.755+0800","caller":"logger/logger.go:94","msg":"/api/vi/community","status":404,"method":"GET","path":"/api/vi/community","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:08:27.039+0800","caller":"logger/logger.go:94","msg":"/api/vi/community/","status":404,"method":"GET","path":"/api/vi/community/","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:08:29.743+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0011616}
{"level":"DEBUG","time":"2025-08-24T18:09:04.950+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:09:04.960+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:09:04.961+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:09:04.962+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:09:50.132+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:09:50.142+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:09:50.142+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:09:50.143+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:10:21.196+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:10:21.206+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:10:21.206+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:10:21.207+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:10:30.835+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:10:30.844+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:10:30.845+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:10:30.845+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:10:45.882+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:10:45.905+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:10:45.905+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:10:45.906+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:10:52.858+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:10:52.868+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:10:52.869+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:10:52.870+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:11:16.632+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:11:16.644+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:11:16.646+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:11:16.646+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T18:11:32.250+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/1","status":404,"method":"GET","path":"/api/v1/community/1","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T18:19:32.435+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:19:32.446+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:19:32.446+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:19:32.447+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:21:11.967+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:21:11.978+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:21:11.979+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:21:11.980+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:21:24.295+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:21:24.305+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:21:24.306+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:21:24.307+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:22:45.254+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:22:45.268+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:22:45.269+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:22:45.271+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:23:07.957+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:23:07.966+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:23:07.967+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:23:07.967+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T18:23:13.687+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/1","status":404,"method":"GET","path":"/api/v1/community/1","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:23:14.964+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/1","status":404,"method":"GET","path":"/api/v1/community/1","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T18:23:19.933+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010556}
{"level":"INFO","time":"2025-08-24T18:23:22.563+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/1","status":404,"method":"GET","path":"/api/v1/community/1","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T18:27:19.383+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:27:19.395+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:27:19.395+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:27:19.397+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:30:23.607+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:30:23.620+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:30:23.620+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:30:23.622+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:32:39.017+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:32:39.045+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:32:39.047+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:32:39.048+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T18:32:56.802+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/1","status":200,"method":"GET","path":"/api/v1/community/1","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0115397}
{"level":"ERROR","time":"2025-08-24T18:49:42.187+0800","caller":"controllers/community.go:27","msg":"CommunityDetailHandler failed","error":"strconv.ParseInt: parsing \"hhh\": invalid syntax"}
{"level":"INFO","time":"2025-08-24T18:49:42.187+0800","caller":"logger/logger.go:94","msg":"/api/v1/community/hhh","status":200,"method":"GET","path":"/api/v1/community/hhh","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0009964}
{"level":"DEBUG","time":"2025-08-24T18:52:01.039+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:52:01.057+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:52:01.058+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:52:01.061+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:52:16.964+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:52:16.977+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:52:16.978+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:52:16.979+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:52:36.163+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:52:36.175+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:52:36.177+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:52:36.180+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:52:50.639+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:52:50.651+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:52:50.652+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:52:50.653+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:53:25.360+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:53:25.368+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:53:25.369+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:53:25.369+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T18:53:55.464+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T18:53:55.478+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T18:53:55.479+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T18:53:55.494+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:20:25.899+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:20:25.914+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:20:25.915+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:20:25.916+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:21:32.552+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:21:32.561+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:21:32.562+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:21:32.562+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:22:49.482+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:22:49.492+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:22:49.493+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:22:49.494+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:23:00.098+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:23:00.112+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:23:00.113+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:23:00.114+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:24:13.427+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:24:13.446+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:24:13.447+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:24:13.449+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:24:14.403+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:24:14.415+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:24:14.416+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:24:14.418+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:24:58.237+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:24:58.288+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:24:58.290+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:24:58.291+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:25:08.555+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:25:08.565+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:25:08.565+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:25:08.566+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:27:11.192+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:27:11.201+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:27:11.201+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:27:11.202+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:27:23.727+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:27:23.736+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:27:23.737+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:27:23.740+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:27:44.037+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:27:44.047+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:27:44.048+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:27:44.048+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:27:55.802+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:27:55.812+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:27:55.812+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:27:55.816+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:28:09.741+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:28:09.750+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:28:09.751+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:28:09.751+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:29:02.472+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:29:02.481+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:29:02.483+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:29:02.483+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:29:17.378+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:18.049+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0010989}
{"level":"INFO","time":"2025-08-24T20:29:23.829+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:24.989+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:25.559+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:27.291+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:29.914+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:30.461+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:30.956+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:31.110+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:31.204+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:31.266+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:33.442+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:33.939+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:29:34.108+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T20:32:15.201+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:32:15.210+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:32:15.211+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:32:15.212+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:32:19.938+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:32:20.942+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001219}
{"level":"INFO","time":"2025-08-24T20:32:26.691+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:32:27.446+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:32:27.616+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:32:27.775+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T20:33:15.687+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:33:15.701+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:33:15.702+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:33:15.703+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:33:16.186+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015867}
{"level":"INFO","time":"2025-08-24T20:33:20.379+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":404,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T20:41:09.512+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:41:09.536+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:41:09.539+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:41:09.550+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:41:42.165+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:41:42.175+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:41:42.175+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:41:42.176+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:43:37.783+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:43:37.796+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:43:37.796+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:43:37.800+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:43:54.216+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:43:54.227+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:43:54.228+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:43:54.231+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:45:29.671+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:45:29.683+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:45:29.684+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:45:29.686+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:48:21.536+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:48:21.549+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:48:21.550+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:48:21.553+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:48:27.033+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:48:27.044+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:48:27.045+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:48:27.056+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:49:19.253+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:49:19.262+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:49:19.262+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:49:19.264+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:49:25.606+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:49:25.618+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:49:25.619+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:49:25.622+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:51:30.900+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:51:30.911+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:51:30.912+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:51:30.912+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:52:02.067+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:52:02.077+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:52:02.077+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:52:02.078+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T20:52:31.116+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:52:31.129+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:52:31.130+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:52:31.132+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:52:52.275+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0061716}
{"level":"INFO","time":"2025-08-24T20:52:57.628+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:52:58.924+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0004905}
{"level":"INFO","time":"2025-08-24T20:52:59.337+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005099}
{"level":"INFO","time":"2025-08-24T20:52:59.819+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:52:59.959+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:00.087+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:00.147+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:01.434+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:02.024+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005554}
{"level":"INFO","time":"2025-08-24T20:53:02.176+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:02.738+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T20:53:09.457+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T20:53:09.457+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0071286}
{"level":"ERROR","time":"2025-08-24T20:53:12.630+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-24T20:53:12.630+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015616}
{"level":"INFO","time":"2025-08-24T20:53:19.954+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015485}
{"level":"INFO","time":"2025-08-24T20:53:20.218+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:53:22.992+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0008994}
{"level":"ERROR","time":"2025-08-24T20:53:25.763+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:53:25.763+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:54:25.824+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:56:36.020+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T20:56:44.673+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:56:44.673+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0007727}
{"level":"ERROR","time":"2025-08-24T20:56:44.935+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:56:44.935+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015238}
{"level":"ERROR","time":"2025-08-24T20:56:45.153+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:56:45.153+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T20:56:45.342+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:56:45.342+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T20:56:45.512+0800","caller":"controllers/post.go:21","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag\nKey: 'Post.author_id' Error:Field validation for 'author_id' failed on the 'required' tag\nKey: 'Post.post_id' Error:Field validation for 'post_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-24T20:56:45.512+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005143}
{"level":"DEBUG","time":"2025-08-24T20:57:04.234+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:57:04.247+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:57:04.248+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:57:04.249+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:57:04.467+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:57:06.589+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:57:07.623+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T20:57:52.760+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:57:52.772+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:57:52.773+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:57:52.773+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:58:11.158+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T20:58:11.159+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T20:58:11.159+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T20:58:11.159+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T20:58:45.030+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T20:58:45.040+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T20:58:45.041+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T20:58:45.041+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T20:58:47.289+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T20:58:49.720+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T21:00:43.745+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:00:43.755+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:00:43.756+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:00:43.758+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T21:00:44.453+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005955}
{"level":"INFO","time":"2025-08-24T21:00:47.724+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0006867}
{"level":"INFO","time":"2025-08-24T21:00:52.665+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0243845}
{"level":"INFO","time":"2025-08-24T21:00:52.688+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T21:06:26.348+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T21:06:26.348+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T21:06:26.348+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T21:06:26.348+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T21:49:23.854+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:49:23.865+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:49:23.866+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:49:23.866+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T21:49:28.478+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T21:49:28.478+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T21:49:28.478+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T21:49:28.478+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T21:49:31.940+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:49:31.950+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:49:31.950+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:49:31.951+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T21:49:35.869+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T21:49:38.569+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T21:50:11.349+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":404,"method":"GET","path":"/api/v1/post/1","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T21:50:20.958+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/2","status":404,"method":"GET","path":"/api/v1/post/2","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T21:51:08.179+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:51:08.189+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:51:08.190+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:51:08.191+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:51:54.822+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:51:54.832+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:51:54.832+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:51:54.834+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:54:26.594+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:54:26.605+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:54:26.606+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:54:26.607+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:54:52.713+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:54:52.722+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:54:52.723+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:54:52.724+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:54:58.223+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:54:58.233+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:54:58.234+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:54:58.235+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:55:15.981+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:55:15.991+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:55:15.992+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:55:15.992+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:55:56.690+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:55:56.700+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:55:56.701+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:55:56.701+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T21:56:34.223+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:56:34.236+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:56:34.237+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:56:34.238+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T21:56:48.278+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T21:56:48.278+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T21:56:48.278+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T21:56:48.279+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T21:57:01.143+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:57:01.153+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:57:01.154+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:57:01.155+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T21:57:04.137+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T21:57:08.965+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":404,"method":"GET","path":"/api/v1/post/1","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T21:59:16.352+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T21:59:16.365+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T21:59:16.368+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T21:59:16.371+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:00:12.730+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:00:12.740+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:00:12.741+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:00:12.742+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-24T22:00:14.698+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"nil pointer passed to StructScan destination"}
{"level":"INFO","time":"2025-08-24T22:00:14.698+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":200,"method":"GET","path":"/api/v1/post/1","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.004148}
{"level":"ERROR","time":"2025-08-24T22:00:16.893+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"nil pointer passed to StructScan destination"}
{"level":"INFO","time":"2025-08-24T22:00:16.893+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":200,"method":"GET","path":"/api/v1/post/1","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014286}
{"level":"INFO","time":"2025-08-24T22:13:25.557+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":404,"method":"POST","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:13:27.023+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":404,"method":"POST","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-24T22:13:55.128+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:13:55.138+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:13:55.138+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:13:55.139+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:03.217+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:03.228+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:03.228+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:03.229+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:14.112+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:14.124+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:14.124+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:14.125+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:22.984+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:22.995+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:22.995+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:22.996+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:44.039+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:44.050+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:44.050+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:44.051+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:54.114+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:54.126+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:54.127+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:54.129+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:14:55.370+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:14:55.385+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:14:55.386+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:14:55.388+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:15:00.427+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:15:00.439+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:15:00.439+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:15:00.440+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T22:15:05.043+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T22:15:05.043+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T22:15:05.043+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T22:15:05.043+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T22:15:20.589+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:15:20.600+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:15:20.601+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:15:20.601+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-24T22:15:28.088+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:15:28.088+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":200,"method":"GET","path":"/api/v1/post/1","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0021158}
{"level":"INFO","time":"2025-08-24T22:15:31.706+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":404,"method":"POST","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:15:32.865+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":404,"method":"POST","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:15:33.657+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":404,"method":"POST","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T22:15:48.046+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:15:48.047+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010009}
{"level":"DEBUG","time":"2025-08-24T22:17:28.533+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:17:28.543+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:17:28.544+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:17:28.544+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:17:51.676+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:17:51.688+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:17:51.690+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:17:51.691+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-24T22:19:22.353+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:19:22.353+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.007918}
{"level":"ERROR","time":"2025-08-24T22:19:23.355+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:19:23.355+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.000628}
{"level":"ERROR","time":"2025-08-24T22:19:38.594+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:19:38.594+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005094}
{"level":"ERROR","time":"2025-08-24T22:20:19.684+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:19.684+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.001086}
{"level":"INFO","time":"2025-08-24T22:20:23.484+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T22:20:23.485+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T22:20:23.485+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T22:20:23.485+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-24T22:20:36.322+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:20:36.330+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:20:36.331+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:20:36.331+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-24T22:20:41.411+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:41.411+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0052264}
{"level":"ERROR","time":"2025-08-24T22:20:42.403+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:42.403+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005281}
{"level":"ERROR","time":"2025-08-24T22:20:43.279+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:43.279+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0015069}
{"level":"ERROR","time":"2025-08-24T22:20:45.883+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:45.883+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360961","status":200,"method":"GET","path":"/api/v1/post/1283677816360961","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006228}
{"level":"ERROR","time":"2025-08-24T22:20:49.314+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:49.314+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-24T22:20:56.741+0800","caller":"controllers/post.go:48","msg":"GetPostDetailHandler failed","error":"Error 1052 (23000): Column 'community_id' in field list is ambiguous"}
{"level":"INFO","time":"2025-08-24T22:20:56.741+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016826}
{"level":"DEBUG","time":"2025-08-24T22:21:53.272+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:21:53.285+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:21:53.286+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:21:53.287+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:22:28.024+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:22:28.038+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:22:28.038+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:22:28.040+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-24T22:22:45.280+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-24T22:22:45.290+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-24T22:22:45.291+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-24T22:22:45.292+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-24T22:22:46.081+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0021015}
{"level":"INFO","time":"2025-08-24T22:30:18.179+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014725}
{"level":"INFO","time":"2025-08-24T22:30:23.428+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:24.050+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:24.379+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:24.573+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:24.772+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:25.082+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:30:25.214+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/posts2","query":"page=1&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-24T22:31:20.691+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-24T22:31:20.693+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-24T22:31:20.693+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-24T22:31:20.694+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T14:41:04.223+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T14:41:04.244+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T14:41:04.252+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T14:41:04.254+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T14:41:19.529+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0296014}
{"level":"INFO","time":"2025-08-25T14:41:24.688+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001238}
{"level":"INFO","time":"2025-08-25T14:41:37.328+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:39.505+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0013373}
{"level":"INFO","time":"2025-08-25T14:41:41.075+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005384}
{"level":"INFO","time":"2025-08-25T14:41:41.803+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:41.987+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:42.063+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:42.159+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:42.311+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:44.735+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0006207}
{"level":"INFO","time":"2025-08-25T14:41:47.411+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:47.584+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:47.673+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:51.384+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:41:51.932+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0009058}
{"level":"ERROR","time":"2025-08-25T14:41:58.018+0800","caller":"controllers/user.go:59","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:41:58.018+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T14:42:06.737+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0053531}
{"level":"INFO","time":"2025-08-25T14:42:07.000+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0035206}
{"level":"INFO","time":"2025-08-25T14:42:09.953+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0010567}
{"level":"INFO","time":"2025-08-25T14:42:14.833+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0346327}
{"level":"INFO","time":"2025-08-25T14:42:15.070+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.006338}
{"level":"INFO","time":"2025-08-25T14:42:16.320+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0008017}
{"level":"ERROR","time":"2025-08-25T14:42:23.314+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:23.314+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0051906}
{"level":"ERROR","time":"2025-08-25T14:42:23.719+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:23.719+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005797}
{"level":"ERROR","time":"2025-08-25T14:42:24.238+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:24.238+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0003968}
{"level":"ERROR","time":"2025-08-25T14:42:24.427+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:24.427+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005058}
{"level":"ERROR","time":"2025-08-25T14:42:24.504+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:24.505+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0009302}
{"level":"ERROR","time":"2025-08-25T14:42:24.581+0800","caller":"controllers/post.go:22","msg":"CreatePostHandler failed","error":"Key: 'Post.community_id' Error:Field validation for 'community_id' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-25T14:42:24.581+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005045}
{"level":"INFO","time":"2025-08-25T14:42:27.436+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0057631}
{"level":"INFO","time":"2025-08-25T14:42:27.763+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005721}
{"level":"INFO","time":"2025-08-25T14:42:32.862+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=2&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0018289}
{"level":"INFO","time":"2025-08-25T14:42:37.553+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001952}
{"level":"INFO","time":"2025-08-25T14:42:39.896+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011269}
{"level":"INFO","time":"2025-08-25T14:42:41.670+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=2&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0154738}
{"level":"INFO","time":"2025-08-25T14:42:43.795+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011656}
{"level":"INFO","time":"2025-08-25T15:26:36.543+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T15:26:36.544+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T15:26:36.544+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T15:26:36.547+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T22:55:50.084+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T22:55:50.106+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T22:55:50.108+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T22:55:50.109+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T22:55:56.756+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:55:59.511+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:06.452+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:07.799+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:08.361+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0065179}
{"level":"INFO","time":"2025-08-25T22:56:12.375+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:13.387+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:13.866+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0007583}
{"level":"INFO","time":"2025-08-25T22:56:14.022+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:14.111+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:14.191+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:14.349+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:14.504+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:14.689+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005232}
{"level":"INFO","time":"2025-08-25T22:56:14.848+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:15.005+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:16.066+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:24.190+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0079885}
{"level":"INFO","time":"2025-08-25T22:56:24.446+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:26.905+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005077}
{"level":"INFO","time":"2025-08-25T22:56:31.535+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.016217}
{"level":"INFO","time":"2025-08-25T22:56:31.766+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:33.760+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T22:56:34.568+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-25T22:57:26.471+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T22:57:26.489+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T22:57:26.491+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T22:57:26.494+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T22:57:31.491+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T22:57:31.491+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T22:57:31.492+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T22:57:31.493+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T22:58:03.688+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T22:58:03.705+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T22:58:03.705+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T22:58:03.706+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-25T22:58:34.889+0800","caller":"logic/vote.go:146","msg":"获取帖子列表失败","error":"Error 1054 (42S22): Unknown column 'p.vote_num' in 'field list'"}
{"level":"ERROR","time":"2025-08-25T22:58:34.889+0800","caller":"controllers/post.go:72","msg":"GetPostListHandler failed","error":"Error 1054 (42S22): Unknown column 'p.vote_num' in 'field list'"}
{"level":"INFO","time":"2025-08-25T22:58:34.889+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0056943}
{"level":"INFO","time":"2025-08-25T22:58:47.971+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T22:58:47.972+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T22:58:47.972+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T22:58:47.972+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:00:22.028+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:00:22.040+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:00:22.040+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:00:22.041+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-25T23:00:39.958+0800","caller":"mysql/vote.go:182","msg":"批量查询帖子投票统计失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:00:39.959+0800","caller":"logic/vote.go:201","msg":"批量获取投票统计失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:00:39.959+0800","caller":"logic/vote.go:154","msg":"获取投票信息失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:00:39.959+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.012769}
{"level":"ERROR","time":"2025-08-25T23:00:47.212+0800","caller":"mysql/vote.go:182","msg":"批量查询帖子投票统计失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:00:47.212+0800","caller":"logic/vote.go:201","msg":"批量获取投票统计失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:00:47.212+0800","caller":"logic/vote.go:154","msg":"获取投票信息失败","error":"missing destination name post_id in *[]models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:00:47.212+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0023089}
{"level":"INFO","time":"2025-08-25T23:00:55.030+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.000709}
{"level":"INFO","time":"2025-08-25T23:00:56.180+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:01:02.583+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:01:03.653+0800","caller":"mysql/vote.go:91","msg":"投票操作成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_change":1}
{"level":"INFO","time":"2025-08-25T23:01:03.658+0800","caller":"redis/vote.go:100","msg":"Redis投票缓存更新成功","post_id":1675169445711872,"user_id":505376604688384,"direction":1,"score":0.006044444444444444}
{"level":"ERROR","time":"2025-08-25T23:01:03.660+0800","caller":"mysql/vote.go:135","msg":"查询帖子投票统计失败","post_id":1675169445711872,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:01:03.660+0800","caller":"logic/vote.go:60","msg":"获取投票统计失败","error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:01:03.660+0800","caller":"controllers/vote.go:32","msg":"投票操作失败","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:01:03.660+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0180033}
{"level":"INFO","time":"2025-08-25T23:01:50.524+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:01:50.524+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:01:50.524+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:01:50.525+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:02:04.627+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:02:04.640+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:02:04.641+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:02:04.642+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:02:13.177+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:13.986+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:24.413+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.003941}
{"level":"INFO","time":"2025-08-25T23:02:25.416+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:25.999+0800","caller":"mysql/vote.go:91","msg":"投票操作成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_change":0}
{"level":"INFO","time":"2025-08-25T23:02:26.000+0800","caller":"redis/vote.go:100","msg":"Redis投票缓存更新成功","post_id":1675169445711872,"user_id":505376604688384,"direction":1,"score":0.007866666666666666}
{"level":"ERROR","time":"2025-08-25T23:02:26.001+0800","caller":"mysql/vote.go:135","msg":"查询帖子投票统计失败","post_id":1675169445711872,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:02:26.001+0800","caller":"logic/vote.go:60","msg":"获取投票统计失败","error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:02:26.001+0800","caller":"controllers/vote.go:32","msg":"投票操作失败","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:02:26.001+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0109831}
{"level":"INFO","time":"2025-08-25T23:02:27.614+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:47.129+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:47.790+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:02:49.640+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0006282}
{"level":"INFO","time":"2025-08-25T23:02:54.326+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0066277}
{"level":"INFO","time":"2025-08-25T23:02:54.653+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:03:01.516+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:03:01.516+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:03:01.516+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:03:01.516+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:03:11.023+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:03:11.033+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:03:11.033+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:03:11.035+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:03:16.963+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:03:17.727+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:04:30.872+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0030135}
{"level":"INFO","time":"2025-08-25T23:04:38.524+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0012861}
{"level":"INFO","time":"2025-08-25T23:04:43.109+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:04:43.680+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:04:55.348+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:05:36.607+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:05:49.159+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:06:17.849+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0015015}
{"level":"INFO","time":"2025-08-25T23:06:23.340+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.000506}
{"level":"ERROR","time":"2025-08-25T23:06:23.611+0800","caller":"logic/post.go:39","msg":"GetPostList failed","error":"Error 1052 (23000): Column 'create_time' in order clause is ambiguous"}
{"level":"ERROR","time":"2025-08-25T23:06:23.611+0800","caller":"controllers/post.go:72","msg":"GetPostListHandler failed","error":"Error 1052 (23000): Column 'create_time' in order clause is ambiguous"}
{"level":"INFO","time":"2025-08-25T23:06:23.611+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0040658}
{"level":"INFO","time":"2025-08-25T23:06:24.331+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":204,"method":"OPTIONS","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:06:24.648+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0018548}
{"level":"INFO","time":"2025-08-25T23:06:24.899+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":204,"method":"OPTIONS","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:06:24.973+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0018913}
{"level":"INFO","time":"2025-08-25T23:06:25.211+0800","caller":"mysql/vote.go:91","msg":"投票操作成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_change":0}
{"level":"INFO","time":"2025-08-25T23:06:25.213+0800","caller":"redis/vote.go:100","msg":"Redis投票缓存更新成功","post_id":1675169445711872,"user_id":505376604688384,"direction":1,"score":0.3142299956639812}
{"level":"ERROR","time":"2025-08-25T23:06:25.214+0800","caller":"mysql/vote.go:135","msg":"查询帖子投票统计失败","post_id":1675169445711872,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:06:25.214+0800","caller":"logic/vote.go:60","msg":"获取投票统计失败","error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:06:25.214+0800","caller":"controllers/vote.go:32","msg":"投票操作失败","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:06:25.214+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0138459}
{"level":"INFO","time":"2025-08-25T23:06:33.921+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:07:08.203+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:07:16.998+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:07:17.935+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0016733}
{"level":"INFO","time":"2025-08-25T23:07:52.869+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:08:08.609+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0012773}
{"level":"INFO","time":"2025-08-25T23:08:10.039+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":204,"method":"OPTIONS","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:08:10.347+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0005112}
{"level":"INFO","time":"2025-08-25T23:08:10.608+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":204,"method":"OPTIONS","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:08:10.670+0800","caller":"mysql/vote.go:91","msg":"投票操作成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_change":0}
{"level":"INFO","time":"2025-08-25T23:08:10.670+0800","caller":"redis/vote.go:100","msg":"Redis投票缓存更新成功","post_id":1675169445711872,"user_id":505376604688384,"direction":1,"score":0.4926545880529957}
{"level":"ERROR","time":"2025-08-25T23:08:10.671+0800","caller":"mysql/vote.go:135","msg":"查询帖子投票统计失败","post_id":1675169445711872,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:08:10.671+0800","caller":"logic/vote.go:60","msg":"获取投票统计失败","error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"ERROR","time":"2025-08-25T23:08:10.671+0800","caller":"controllers/vote.go:32","msg":"投票操作失败","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"error":"missing destination name up_votes in *models.PostVoteInfo"}
{"level":"INFO","time":"2025-08-25T23:08:10.671+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.013918}
{"level":"ERROR","time":"2025-08-25T23:08:10.918+0800","caller":"logic/post.go:39","msg":"GetPostList failed","error":"Error 1052 (23000): Column 'create_time' in order clause is ambiguous"}
{"level":"ERROR","time":"2025-08-25T23:08:10.918+0800","caller":"controllers/post.go:72","msg":"GetPostListHandler failed","error":"Error 1052 (23000): Column 'create_time' in order clause is ambiguous"}
{"level":"INFO","time":"2025-08-25T23:08:10.918+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0005114}
{"level":"INFO","time":"2025-08-25T23:08:13.026+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0016919}
{"level":"INFO","time":"2025-08-25T23:08:17.251+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:08:33.156+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:09:31.010+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:09:31.010+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:09:31.011+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:09:31.012+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:10:13.500+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:10:13.511+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:10:13.511+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:10:13.512+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:10:25.545+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:26.164+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:26.661+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:26.886+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.076+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.240+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.402+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.547+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.726+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:27.870+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:28.021+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:31.039+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=5&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.0033803}
{"level":"INFO","time":"2025-08-25T23:10:31.663+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:35.408+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:35.991+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:36.180+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:36.310+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:51.759+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:10:58.005+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:11:00.034+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.002435}
{"level":"INFO","time":"2025-08-25T23:11:00.057+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:11:42.143+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=3","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.005402}
{"level":"INFO","time":"2025-08-25T23:11:44.611+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=3","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0020687}
{"level":"INFO","time":"2025-08-25T23:12:48.026+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:12:48.369+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:12:57.671+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:13.158+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:13.163+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:13.393+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:21.741+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:22.953+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:30.398+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:35.107+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:35.535+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:48.494+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:49.315+0800","caller":"logger/logger.go:94","msg":"/api/v1/api/v1/posts2","status":404,"method":"GET","path":"/api/v1/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:13:54.387+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011899}
{"level":"INFO","time":"2025-08-25T23:13:54.399+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0019617}
{"level":"INFO","time":"2025-08-25T23:13:54.425+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0010424}
{"level":"INFO","time":"2025-08-25T23:13:55.425+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016869}
{"level":"INFO","time":"2025-08-25T23:14:05.845+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:14:05.846+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:14:05.846+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:14:05.847+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:14:37.755+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:14:37.768+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:14:37.768+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:14:37.770+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:14:44.680+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0052555}
{"level":"INFO","time":"2025-08-25T23:14:44.892+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014907}
{"level":"INFO","time":"2025-08-25T23:15:50.870+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0035382}
{"level":"DEBUG","time":"2025-08-25T23:16:47.629+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:16:47.640+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:16:47.641+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:16:47.642+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:17:02.046+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:17:02.056+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:17:02.057+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:17:02.057+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:17:56.200+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:17:56.210+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:17:56.210+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:17:56.212+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:18:36.237+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:18:36.247+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:18:36.248+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:18:36.248+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:18:49.382+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:18:49.396+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:18:49.397+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:18:49.398+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:18:59.227+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:18:59.236+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:18:59.237+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:18:59.238+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:19:12.142+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:19:12.156+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:19:12.157+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:19:12.157+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:19:31.195+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:19:31.203+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:19:31.204+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:19:31.205+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:19:50.630+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:19:50.638+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:19:50.639+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:19:50.639+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:20:13.613+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:20:13.624+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:20:13.625+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:20:13.626+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:20:28.977+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:20:28.988+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:20:28.989+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:20:28.989+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:20:42.651+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:20:42.662+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:20:42.663+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:20:42.663+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:21:11.516+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0025232}
{"level":"DEBUG","time":"2025-08-25T23:22:34.193+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:22:34.205+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:22:34.205+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:22:34.207+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:22:50.492+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:22:50.506+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:22:50.507+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:22:50.508+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:23:13.601+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:23:13.614+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:23:13.615+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:23:13.617+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:23:25.424+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:23:25.434+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:23:25.434+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:23:25.435+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:24:20.240+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:24:20.253+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:24:20.254+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:24:20.255+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:24:31.931+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:24:31.944+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:24:31.945+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:24:31.946+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:25:03.279+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:25:03.292+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:25:03.293+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:25:03.294+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:25:39.816+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:25:39.828+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:25:39.829+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:25:39.830+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:26:21.197+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0063416}
{"level":"INFO","time":"2025-08-25T23:26:32.560+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016589}
{"level":"DEBUG","time":"2025-08-25T23:27:28.042+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:27:28.053+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:27:28.053+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:27:28.054+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:27:49.675+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:27:49.685+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:27:49.685+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:27:49.686+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:28:31.283+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:28:31.293+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:28:31.294+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:28:31.295+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:28:50.928+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:28:50.940+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:28:50.941+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:28:50.942+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:29:03.166+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:29:03.183+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:29:03.183+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:29:03.184+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:29:16.511+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:29:16.524+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:29:16.524+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:29:16.526+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:29:38.270+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:29:38.304+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:29:38.306+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:29:38.306+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:29:38.307+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:29:44.803+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:29:44.812+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:29:44.815+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:29:44.816+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:29:44.816+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:30:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-25T23:30:00.029+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":5}
{"level":"DEBUG","time":"2025-08-25T23:30:16.371+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:30:16.387+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:30:16.388+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:30:16.389+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:30:16.392+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:30:52.280+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:30:52.281+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:30:52.281+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-25T23:30:52.281+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:30:52.282+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:31:13.540+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:31:13.551+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:31:13.551+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:31:13.551+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:31:13.552+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:31:19.046+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0085155}
{"level":"INFO","time":"2025-08-25T23:31:22.043+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1550833124642816","status":200,"method":"GET","path":"/api/v1/post/1550833124642816","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0018286}
{"level":"INFO","time":"2025-08-25T23:31:25.940+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0026809}
{"level":"INFO","time":"2025-08-25T23:31:59.156+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014108}
{"level":"ERROR","time":"2025-08-25T23:32:08.564+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-25T23:32:08.564+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.00152}
{"level":"ERROR","time":"2025-08-25T23:32:11.607+0800","caller":"controllers/user.go:81","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-25T23:32:11.608+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0009509}
{"level":"INFO","time":"2025-08-25T23:32:14.191+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016459}
{"level":"INFO","time":"2025-08-25T23:32:14.527+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005951}
{"level":"INFO","time":"2025-08-25T23:32:18.612+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1550780146388992","status":200,"method":"GET","path":"/api/v1/post/1550780146388992","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0017284}
{"level":"INFO","time":"2025-08-25T23:32:24.884+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0021161}
{"level":"INFO","time":"2025-08-25T23:32:25.849+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1675169445711872","status":200,"method":"GET","path":"/api/v1/post/1675169445711872","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012883}
{"level":"INFO","time":"2025-08-25T23:32:27.389+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005107}
{"level":"INFO","time":"2025-08-25T23:32:30.217+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1550780146388992","status":200,"method":"GET","path":"/api/v1/post/1550780146388992","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0022255}
{"level":"INFO","time":"2025-08-25T23:32:33.688+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005103}
{"level":"INFO","time":"2025-08-25T23:34:32.564+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.003081}
{"level":"INFO","time":"2025-08-25T23:34:34.694+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0024694}
{"level":"INFO","time":"2025-08-25T23:34:40.689+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001314}
{"level":"INFO","time":"2025-08-25T23:34:47.603+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0087009}
{"level":"INFO","time":"2025-08-25T23:34:47.846+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012325}
{"level":"INFO","time":"2025-08-25T23:34:51.197+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1684799886462976","status":200,"method":"GET","path":"/api/v1/post/1684799886462976","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016202}
{"level":"INFO","time":"2025-08-25T23:34:55.043+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0013685}
{"level":"INFO","time":"2025-08-25T23:34:55.902+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015434}
{"level":"INFO","time":"2025-08-25T23:38:49.497+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012963}
{"level":"INFO","time":"2025-08-25T23:40:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-25T23:40:00.006+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":6}
{"level":"DEBUG","time":"2025-08-25T23:40:25.274+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:40:25.296+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:40:25.297+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:40:25.297+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:40:25.298+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:40:26.411+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:40:26.422+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:40:26.423+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:40:26.424+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:40:26.424+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:40:58.992+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:40:59.004+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:40:59.004+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:40:59.005+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:40:59.006+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:41:47.653+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:41:47.667+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:41:47.668+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:41:47.668+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:41:47.668+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:41:54.062+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:41:54.063+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:41:54.063+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-25T23:41:54.063+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:41:54.063+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:42:49.424+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:42:49.439+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:42:49.440+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:42:49.440+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:42:49.440+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:43:26.725+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0095238}
{"level":"INFO","time":"2025-08-25T23:43:40.790+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001274}
{"level":"INFO","time":"2025-08-25T23:43:45.700+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1684799886462976","status":200,"method":"GET","path":"/api/v1/post/1684799886462976","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0031905}
{"level":"INFO","time":"2025-08-25T23:43:49.108+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012946}
{"level":"INFO","time":"2025-08-25T23:44:06.037+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1684799886462976","status":200,"method":"GET","path":"/api/v1/post/1684799886462976","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0007996}
{"level":"INFO","time":"2025-08-25T23:44:07.759+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001616}
{"level":"INFO","time":"2025-08-25T23:44:10.557+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0060606}
{"level":"INFO","time":"2025-08-25T23:44:10.890+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001182}
{"level":"INFO","time":"2025-08-25T23:44:12.866+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=score","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011533}
{"level":"INFO","time":"2025-08-25T23:44:19.009+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1283677816360960","status":200,"method":"GET","path":"/api/v1/post/1283677816360960","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014543}
{"level":"INFO","time":"2025-08-25T23:44:22.094+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015046}
{"level":"INFO","time":"2025-08-25T23:45:30.146+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033115}
{"level":"DEBUG","time":"2025-08-25T23:46:02.777+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:46:02.791+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:46:02.792+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:46:02.793+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:46:02.794+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:46:21.666+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:46:21.676+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:46:21.677+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:46:21.678+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:46:21.678+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:46:33.841+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:46:33.853+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:46:33.854+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:46:33.855+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:46:33.856+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:47:18.013+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":204,"method":"OPTIONS","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:47:18.019+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0045392}
{"level":"INFO","time":"2025-08-25T23:47:20.194+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":204,"method":"OPTIONS","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:47:20.196+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:47:20.196+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:47:20.196+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-25T23:47:20.196+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:47:20.197+0800","caller":"redis/vote.go:55","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:47:20.198+0800","caller":"redis/vote.go:67","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:47:20.199+0800","caller":"redis/vote.go:120","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.703111111111}
{"level":"INFO","time":"2025-08-25T23:47:20.199+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:47:20.199+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0033775}
{"level":"INFO","time":"2025-08-25T23:47:24.234+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":204,"method":"OPTIONS","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:47:24.235+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:47:24.235+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:47:24.235+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":-1}
{"level":"INFO","time":"2025-08-25T23:47:24.235+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:47:24.237+0800","caller":"redis/vote.go:55","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-25T23:47:24.237+0800","caller":"redis/vote.go:67","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-25T23:47:24.237+0800","caller":"redis/vote.go:120","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7032}
{"level":"INFO","time":"2025-08-25T23:47:24.237+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-25T23:47:24.237+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0026447}
{"level":"INFO","time":"2025-08-25T23:47:26.325+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote/1684799886462976","status":204,"method":"OPTIONS","path":"/api/v1/vote/1684799886462976","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:47:26.326+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote/1684799886462976","status":200,"method":"GET","path":"/api/v1/vote/1684799886462976","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-25T23:47:46.207+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:47:46.216+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:47:46.217+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:47:46.217+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:47:46.218+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"DEBUG","time":"2025-08-25T23:48:14.624+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:48:14.637+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:48:14.637+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:48:14.638+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:48:14.638+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:50:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-25T23:50:00.008+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":6}
{"level":"INFO","time":"2025-08-25T23:50:29.871+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:50:29.872+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:50:29.872+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-25T23:50:29.872+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:50:29.872+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:51:04.599+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:51:04.612+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:51:04.613+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:51:04.613+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:51:04.614+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:51:07.264+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0035614}
{"level":"INFO","time":"2025-08-25T23:52:10.212+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005246}
{"level":"INFO","time":"2025-08-25T23:52:28.601+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.005076}
{"level":"INFO","time":"2025-08-25T23:52:45.283+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.000505}
{"level":"DEBUG","time":"2025-08-25T23:53:14.356+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:53:14.369+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:53:14.370+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:53:14.370+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:53:14.371+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:53:42.467+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:53:42.467+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:53:42.467+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-25T23:53:42.467+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:53:42.468+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:54:00.824+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:54:00.838+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:54:00.838+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:54:00.838+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:54:00.839+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:54:05.137+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-25T23:54:05.138+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-25T23:54:05.138+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-25T23:54:05.138+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-25T23:54:05.138+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-25T23:54:22.143+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-25T23:54:22.154+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-25T23:54:22.154+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-25T23:54:22.155+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-25T23:54:22.155+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-25T23:54:30.889+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0023696}
{"level":"INFO","time":"2025-08-25T23:54:39.825+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012384}
{"level":"INFO","time":"2025-08-25T23:54:46.581+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0013764}
{"level":"INFO","time":"2025-08-25T23:54:46.597+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011546}
{"level":"INFO","time":"2025-08-25T23:55:58.225+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0013979}
{"level":"INFO","time":"2025-08-25T23:56:38.752+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005509}
{"level":"INFO","time":"2025-08-25T23:57:14.145+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001416}
{"level":"INFO","time":"2025-08-25T23:57:15.508+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:15.508+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:15.508+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:15.508+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:15.511+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:15.512+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:15.512+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:57:15.512+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":-1,"new_direction":1,"vote_change":2,"score_change":3422.7163333333333}
{"level":"INFO","time":"2025-08-25T23:57:15.512+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:57:15.512+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0041873}
{"level":"INFO","time":"2025-08-25T23:57:17.946+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:17.946+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:17.946+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":-1}
{"level":"INFO","time":"2025-08-25T23:57:17.946+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:17.948+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-25T23:57:17.948+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:17.948+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-25T23:57:17.948+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7163777777778}
{"level":"INFO","time":"2025-08-25T23:57:17.949+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-25T23:57:17.949+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0028484}
{"level":"INFO","time":"2025-08-25T23:57:20.023+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:20.023+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:20.023+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1675169445711872,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:20.023+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:20.024+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1675169445711872,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:20.025+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:20.025+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:57:20.026+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1675169445711872,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7164444444443}
{"level":"INFO","time":"2025-08-25T23:57:20.026+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:57:20.026+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0037603}
{"level":"INFO","time":"2025-08-25T23:57:22.611+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:22.611+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:22.611+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550833124642816,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:22.611+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:22.612+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550833124642816,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:22.613+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:22.614+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:57:22.614+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550833124642816,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7164888888888}
{"level":"INFO","time":"2025-08-25T23:57:22.614+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550833124642816,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:57:22.614+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0040976}
{"level":"INFO","time":"2025-08-25T23:57:33.164+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012702}
{"level":"INFO","time":"2025-08-25T23:57:34.164+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:34.164+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:34.164+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:34.164+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:34.165+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:34.165+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:34.166+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:57:34.167+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":-1,"new_direction":1,"vote_change":2,"score_change":3422.7167555555557}
{"level":"INFO","time":"2025-08-25T23:57:34.167+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:57:34.167+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033943}
{"level":"INFO","time":"2025-08-25T23:57:40.978+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:57:40.978+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:57:40.978+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1675169445711872,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:40.978+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:57:40.981+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1675169445711872,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:57:40.981+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:57:40.981+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:57:40.982+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:57:40.982+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0042199}
{"level":"INFO","time":"2025-08-25T23:58:45.013+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote/1675169445711872","status":200,"method":"GET","path":"/api/v1/vote/1675169445711872","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0005195}
{"level":"INFO","time":"2025-08-25T23:58:48.744+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":204,"method":"OPTIONS","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:58:48.748+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0019559}
{"level":"INFO","time":"2025-08-25T23:58:51.772+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":204,"method":"OPTIONS","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:58:51.773+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:58:51.773+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:58:51.773+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1675169445711872,"direction":1}
{"level":"INFO","time":"2025-08-25T23:58:51.773+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:58:51.774+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1675169445711872,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:58:51.774+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:58:51.776+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:58:51.776+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:58:51.776+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.002967}
{"level":"INFO","time":"2025-08-25T23:58:54.064+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote/1675169445711872","status":204,"method":"OPTIONS","path":"/api/v1/vote/1675169445711872","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-25T23:58:54.066+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote/1675169445711872","status":200,"method":"GET","path":"/api/v1/vote/1675169445711872","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0005314}
{"level":"INFO","time":"2025-08-25T23:59:12.601+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012874}
{"level":"INFO","time":"2025-08-25T23:59:14.148+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:14.148+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:14.148+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:14.148+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:14.151+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:14.151+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:14.152+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:14.152+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:14.152+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0031991}
{"level":"INFO","time":"2025-08-25T23:59:22.755+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:22.755+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:22.755+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:22.755+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:22.757+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:22.758+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:22.758+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:22.758+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7191555555555}
{"level":"INFO","time":"2025-08-25T23:59:22.759+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:22.759+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0036985}
{"level":"INFO","time":"2025-08-25T23:59:37.351+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:37.351+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:37.351+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:37.351+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:37.353+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:37.353+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:37.353+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:37.353+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:37.353+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0023397}
{"level":"INFO","time":"2025-08-25T23:59:40.601+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:40.601+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:40.601+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:40.601+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:40.603+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:40.603+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:40.604+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:40.604+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:40.604+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033446}
{"level":"INFO","time":"2025-08-25T23:59:45.135+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:45.135+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:45.135+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:45.136+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:45.140+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:45.141+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:45.142+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:45.142+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.719666666667}
{"level":"INFO","time":"2025-08-25T23:59:45.143+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:45.143+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0078871}
{"level":"INFO","time":"2025-08-25T23:59:47.734+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-25T23:59:47.734+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-25T23:59:47.735+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1283677816360960,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:47.735+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-25T23:59:47.736+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1283677816360960,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-25T23:59:47.737+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-25T23:59:47.737+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-25T23:59:47.738+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1283677816360960,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7197111111113}
{"level":"INFO","time":"2025-08-25T23:59:47.738+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1283677816360960,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-25T23:59:47.738+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033624}
{"level":"INFO","time":"2025-08-26T00:00:00.000+0800","caller":"cron/score_updater.go:112","msg":"开始清理过期数据"}
{"level":"INFO","time":"2025-08-26T00:00:00.000+0800","caller":"cron/score_updater.go:118","msg":"过期数据清理完成"}
{"level":"INFO","time":"2025-08-26T00:00:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-26T00:00:00.000+0800","caller":"cron/score_updater.go:101","msg":"开始同步投票数据"}
{"level":"INFO","time":"2025-08-26T00:00:00.000+0800","caller":"cron/score_updater.go:107","msg":"投票数据同步完成"}
{"level":"INFO","time":"2025-08-26T00:00:00.015+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":6}
{"level":"INFO","time":"2025-08-26T00:00:18.488+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:00:18.488+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:00:18.488+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:00:18.488+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:00:18.490+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:00:18.490+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:00:18.490+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:00:18.490+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:00:18.490+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0022903}
{"level":"INFO","time":"2025-08-26T00:04:28.483+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001141}
{"level":"INFO","time":"2025-08-26T00:04:41.882+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015121}
{"level":"INFO","time":"2025-08-26T00:04:57.498+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0017847}
{"level":"INFO","time":"2025-08-26T00:05:22.810+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014552}
{"level":"INFO","time":"2025-08-26T00:05:23.590+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:23.590+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:23.590+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:23.590+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:23.592+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:23.593+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:23.593+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:23.594+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:23.594+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0036751}
{"level":"INFO","time":"2025-08-26T00:05:24.641+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:24.641+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:24.641+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:24.641+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:24.642+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:24.642+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:24.643+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:24.643+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7271999999998}
{"level":"INFO","time":"2025-08-26T00:05:24.643+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:05:24.643+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0026972}
{"level":"INFO","time":"2025-08-26T00:05:25.849+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:25.849+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:25.849+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:25.849+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:25.852+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:25.853+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:25.853+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:25.853+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":-1,"new_direction":1,"vote_change":2,"score_change":3422.7272222222223}
{"level":"INFO","time":"2025-08-26T00:05:25.853+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:25.853+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0039548}
{"level":"INFO","time":"2025-08-26T00:05:26.588+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:26.588+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:26.588+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:26.588+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:26.590+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:26.591+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:26.591+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:26.591+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:26.591+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0031029}
{"level":"INFO","time":"2025-08-26T00:05:27.227+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:27.227+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:27.227+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:27.227+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:27.230+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:27.230+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:27.230+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:27.231+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7272666666668}
{"level":"INFO","time":"2025-08-26T00:05:27.232+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:05:27.232+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0042038}
{"level":"INFO","time":"2025-08-26T00:05:28.115+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:28.115+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:28.116+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1675169445711872,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:28.116+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:28.118+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1675169445711872,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:28.118+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:28.118+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:28.119+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1675169445711872,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:28.119+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033567}
{"level":"INFO","time":"2025-08-26T00:05:29.100+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:29.100+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:29.100+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550833124642816,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.100+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:29.103+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550833124642816,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.103+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:29.103+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.111+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550833124642816,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:29.112+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0114871}
{"level":"INFO","time":"2025-08-26T00:05:29.973+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:29.973+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:29.973+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.973+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:29.975+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.975+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:29.976+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:29.976+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:29.976+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.003836}
{"level":"INFO","time":"2025-08-26T00:05:30.831+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:30.831+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:30.831+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:30.831+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:30.835+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:30.835+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:30.836+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:05:30.836+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7273333333333}
{"level":"INFO","time":"2025-08-26T00:05:30.837+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:05:30.837+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0058181}
{"level":"INFO","time":"2025-08-26T00:05:31.450+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:31.450+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:31.450+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:31.450+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:31.453+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:31.453+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:31.454+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:31.455+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7273555555557}
{"level":"INFO","time":"2025-08-26T00:05:31.455+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:31.455+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0057346}
{"level":"INFO","time":"2025-08-26T00:05:32.285+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:32.286+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:32.286+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:32.286+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:32.291+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:32.291+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:32.292+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:05:32.292+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7273777777777}
{"level":"INFO","time":"2025-08-26T00:05:32.294+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:05:32.294+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0083285}
{"level":"INFO","time":"2025-08-26T00:05:32.718+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:32.718+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:32.718+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:32.718+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:32.721+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:32.721+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:32.721+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:32.722+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7273777777777}
{"level":"INFO","time":"2025-08-26T00:05:32.722+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:32.722+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0041069}
{"level":"INFO","time":"2025-08-26T00:05:34.414+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:34.414+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:34.414+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:34.414+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:34.418+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:05:34.418+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:34.418+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:05:34.419+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7274222222222}
{"level":"INFO","time":"2025-08-26T00:05:34.420+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:05:34.420+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0063049}
{"level":"INFO","time":"2025-08-26T00:05:34.788+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:34.788+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:34.788+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:34.788+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:34.790+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:34.790+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:34.791+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:34.791+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7274222222222}
{"level":"INFO","time":"2025-08-26T00:05:34.791+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:34.791+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0030771}
{"level":"INFO","time":"2025-08-26T00:05:35.616+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:35.616+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:35.616+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:35.616+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:35.619+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:35.620+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:35.620+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:05:35.621+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7274444444442}
{"level":"INFO","time":"2025-08-26T00:05:35.622+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:05:35.622+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.00575}
{"level":"INFO","time":"2025-08-26T00:05:36.376+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:05:36.376+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:05:36.376+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1550780146388992,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:36.376+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:05:36.378+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1550780146388992,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:05:36.378+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:05:36.379+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:05:36.379+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1550780146388992,"user_id":505376604688384,"old_direction":-1,"new_direction":1,"vote_change":2,"score_change":3422.7274666666667}
{"level":"INFO","time":"2025-08-26T00:05:36.380+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1550780146388992,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:05:36.380+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.004394}
{"level":"INFO","time":"2025-08-26T00:06:20.376+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:20.376+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:20.376+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:20.376+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:20.377+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:20.378+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:20.378+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:06:20.378+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":-1,"new_direction":0,"vote_change":1,"score_change":2990.7284444444444}
{"level":"INFO","time":"2025-08-26T00:06:20.378+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:06:20.378+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0022412}
{"level":"INFO","time":"2025-08-26T00:06:20.904+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:20.904+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:20.904+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:20.904+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:20.905+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:20.905+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:20.905+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:20.909+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":0,"new_direction":-1,"vote_change":-1,"score_change":2126.7284444444444}
{"level":"INFO","time":"2025-08-26T00:06:20.910+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:06:20.910+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.006252}
{"level":"INFO","time":"2025-08-26T00:06:21.821+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:21.821+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:21.821+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:21.821+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:21.821+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:21.822+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:21.822+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:06:21.822+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":-1,"new_direction":0,"vote_change":1,"score_change":2990.7284666666665}
{"level":"INFO","time":"2025-08-26T00:06:21.822+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:06:21.822+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.001402}
{"level":"INFO","time":"2025-08-26T00:06:22.083+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:22.083+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:22.083+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:22.083+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:22.085+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:22.085+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:22.085+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:06:22.086+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":0,"new_direction":-1,"vote_change":-1,"score_change":2126.728488888889}
{"level":"INFO","time":"2025-08-26T00:06:22.086+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:06:22.086+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0026353}
{"level":"INFO","time":"2025-08-26T00:06:23.121+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:23.121+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:23.121+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:23.121+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:23.122+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:23.123+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:23.123+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:06:23.123+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":-1,"new_direction":0,"vote_change":1,"score_change":2990.728511111111}
{"level":"INFO","time":"2025-08-26T00:06:23.123+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:06:23.123+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015629}
{"level":"INFO","time":"2025-08-26T00:06:36.004+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0012344}
{"level":"INFO","time":"2025-08-26T00:06:36.987+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:06:38.101+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","errors":"","cost":0.0009354}
{"level":"INFO","time":"2025-08-26T00:06:47.134+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:47.134+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:47.135+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:47.135+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:47.136+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:47.137+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:47.137+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:06:47.137+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7290444444443}
{"level":"INFO","time":"2025-08-26T00:06:47.137+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:06:47.137+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0030515}
{"level":"INFO","time":"2025-08-26T00:06:47.805+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:47.805+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:47.805+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:06:47.805+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:47.807+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:06:47.808+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:47.808+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:06:47.809+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7290444444443}
{"level":"INFO","time":"2025-08-26T00:06:47.809+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:06:47.809+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0045684}
{"level":"INFO","time":"2025-08-26T00:06:48.755+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:06:48.755+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:06:48.755+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:48.755+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:06:48.759+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:06:48.760+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:06:48.760+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:06:48.761+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7290666666668}
{"level":"INFO","time":"2025-08-26T00:06:48.762+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:06:48.762+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0077929}
{"level":"INFO","time":"2025-08-26T00:06:52.497+0800","caller":"logger/logger.go:94","msg":"/api/v1/community","status":200,"method":"GET","path":"/api/v1/community","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0046029}
{"level":"INFO","time":"2025-08-26T00:06:59.787+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0077133}
{"level":"INFO","time":"2025-08-26T00:07:00.049+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0027583}
{"level":"INFO","time":"2025-08-26T00:07:01.047+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:01.047+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:01.047+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1692904057737216,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:01.047+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1692904057737216,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1692904057737216,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7293555555557}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1692904057737216,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:01.049+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0025261}
{"level":"INFO","time":"2025-08-26T00:07:02.389+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:02.389+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:02.390+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1692904057737216,"direction":0}
{"level":"INFO","time":"2025-08-26T00:07:02.390+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:02.398+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1692904057737216,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:07:02.398+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:02.398+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:07:02.401+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1692904057737216,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7293777777777}
{"level":"INFO","time":"2025-08-26T00:07:02.401+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1692904057737216,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:07:02.402+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0127873}
{"level":"INFO","time":"2025-08-26T00:07:04.302+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:04.302+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:04.303+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:04.303+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:04.304+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:04.305+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:04.306+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:04.306+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.729422222222}
{"level":"INFO","time":"2025-08-26T00:07:04.306+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:04.306+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0035707}
{"level":"INFO","time":"2025-08-26T00:07:06.041+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0022017}
{"level":"INFO","time":"2025-08-26T00:07:07.627+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0024832}
{"level":"INFO","time":"2025-08-26T00:07:08.662+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:08.662+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:08.662+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:08.662+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:08.664+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:08.664+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:08.664+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:08.665+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:08.665+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0033679}
{"level":"INFO","time":"2025-08-26T00:07:12.069+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:12.069+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:12.069+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.069+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7296}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:12.071+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0019472}
{"level":"INFO","time":"2025-08-26T00:07:12.344+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:12.344+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:12.344+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":0}
{"level":"INFO","time":"2025-08-26T00:07:12.344+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:12.346+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:07:12.347+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:12.347+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:07:12.347+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":0,"vote_change":-1,"score_change":2126.7296}
{"level":"INFO","time":"2025-08-26T00:07:12.348+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:07:12.348+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0040409}
{"level":"INFO","time":"2025-08-26T00:07:12.527+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:12.527+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:12.527+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.527+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:12.529+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.529+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:12.530+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:12.530+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.7296}
{"level":"INFO","time":"2025-08-26T00:07:12.530+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:12.530+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0037827}
{"level":"INFO","time":"2025-08-26T00:07:14.228+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0055871}
{"level":"INFO","time":"2025-08-26T00:07:15.610+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:15.610+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:15.610+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:15.610+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:15.612+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:15.612+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:15.612+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:15.613+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:15.613+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0038098}
{"level":"INFO","time":"2025-08-26T00:07:17.067+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:17.067+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:17.067+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:17.067+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:17.069+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:17.069+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:17.069+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:17.069+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:17.070+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0030541}
{"level":"INFO","time":"2025-08-26T00:07:19.592+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0036734}
{"level":"INFO","time":"2025-08-26T00:07:52.115+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:52.115+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:52.115+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.115+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:52.129+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.130+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:52.130+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.131+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:52.131+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0168163}
{"level":"INFO","time":"2025-08-26T00:07:52.816+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:52.816+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:52.816+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1692904057737216,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.816+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:52.818+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1692904057737216,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.818+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:52.818+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":0,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:52.818+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1692904057737216,"user_id":505376604688384,"old_direction":0,"new_direction":1,"vote_change":1,"score_change":2990.730488888889}
{"level":"INFO","time":"2025-08-26T00:07:52.819+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1692904057737216,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:52.819+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0038604}
{"level":"INFO","time":"2025-08-26T00:07:53.870+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015891}
{"level":"INFO","time":"2025-08-26T00:07:55.989+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:07:55.989+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:07:55.989+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1692904057737216,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:55.989+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:07:55.991+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1692904057737216,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:07:55.991+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:07:55.991+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:07:55.992+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1692904057737216,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:07:55.992+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0029936}
{"level":"INFO","time":"2025-08-26T00:07:57.116+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1692904057737216","status":200,"method":"GET","path":"/api/v1/post/1692904057737216","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0595289}
{"level":"INFO","time":"2025-08-26T00:07:58.443+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0015853}
{"level":"INFO","time":"2025-08-26T00:09:10.363+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-26T00:09:10.364+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-26T00:09:10.364+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-26T00:09:10.366+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-26T00:09:10.366+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-26T00:09:28.208+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-26T00:09:28.222+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-26T00:09:28.223+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-26T00:09:28.224+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-26T00:09:28.224+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-26T00:09:40.486+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:40.487+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:40.487+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:40.487+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:40.499+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:40.499+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:40.499+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:09:40.500+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:09:40.501+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0137955}
{"level":"INFO","time":"2025-08-26T00:09:42.077+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.003676}
{"level":"INFO","time":"2025-08-26T00:09:43.685+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:43.685+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:43.686+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:43.686+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:43.687+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:43.688+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:43.688+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:09:43.688+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:09:43.688+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0037888}
{"level":"INFO","time":"2025-08-26T00:09:44.819+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0028585}
{"level":"INFO","time":"2025-08-26T00:09:46.185+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:46.185+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:46.185+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:46.185+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:46.186+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:46.186+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:46.187+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:46.187+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7330222222222}
{"level":"INFO","time":"2025-08-26T00:09:46.187+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:09:46.188+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0028976}
{"level":"INFO","time":"2025-08-26T00:09:47.518+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0029571}
{"level":"INFO","time":"2025-08-26T00:09:48.725+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:48.725+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:48.725+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:48.725+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:48.726+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:48.726+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:48.726+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:09:48.726+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":-1,"new_direction":1,"vote_change":2,"score_change":3422.7330666666667}
{"level":"INFO","time":"2025-08-26T00:09:48.727+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:09:48.727+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0025101}
{"level":"INFO","time":"2025-08-26T00:09:49.590+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:49.590+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:49.590+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:49.590+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:49.591+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:49.591+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:49.592+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:49.592+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7330888888887}
{"level":"INFO","time":"2025-08-26T00:09:49.592+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:09:49.592+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0025}
{"level":"INFO","time":"2025-08-26T00:09:50.296+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1676775029477376","status":200,"method":"GET","path":"/api/v1/post/1676775029477376","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005057}
{"level":"INFO","time":"2025-08-26T00:09:51.511+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0019866}
{"level":"INFO","time":"2025-08-26T00:09:52.599+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:52.599+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:52.599+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:52.599+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:52.599+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:52.600+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:52.600+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:09:52.600+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:09:52.600+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0014142}
{"level":"INFO","time":"2025-08-26T00:09:54.289+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:54.289+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:54.290+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1676775029477376,"direction":0}
{"level":"INFO","time":"2025-08-26T00:09:54.290+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:54.291+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1676775029477376,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:09:54.292+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:54.292+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:09:54.292+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1676775029477376,"user_id":505376604688384,"old_direction":-1,"new_direction":0,"vote_change":1,"score_change":2990.7332}
{"level":"INFO","time":"2025-08-26T00:09:54.292+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1676775029477376,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:09:54.292+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0035224}
{"level":"INFO","time":"2025-08-26T00:09:56.527+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0031557}
{"level":"INFO","time":"2025-08-26T00:09:57.679+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:09:57.679+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:09:57.679+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:57.679+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:09:57.680+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":1}
{"level":"INFO","time":"2025-08-26T00:09:57.680+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:09:57.680+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":1}
{"level":"INFO","time":"2025-08-26T00:09:57.680+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":1,"vote_num":1,"user_vote":1}
{"level":"INFO","time":"2025-08-26T00:09:57.681+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0021891}
{"level":"INFO","time":"2025-08-26T00:09:59.046+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0025886}
{"level":"INFO","time":"2025-08-26T00:10:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-26T00:10:00.010+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":7}
{"level":"INFO","time":"2025-08-26T00:10:00.084+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:10:00.084+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:10:00.084+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:10:00.084+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:10:00.085+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":-1}
{"level":"INFO","time":"2025-08-26T00:10:00.085+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:10:00.086+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":1,"new_direction":-1}
{"level":"INFO","time":"2025-08-26T00:10:00.086+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":1,"new_direction":-1,"vote_change":-2,"score_change":1694.7333333333331}
{"level":"INFO","time":"2025-08-26T00:10:00.086+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":-1,"vote_num":-1,"user_vote":-1}
{"level":"INFO","time":"2025-08-26T00:10:00.086+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0028537}
{"level":"INFO","time":"2025-08-26T00:10:01.515+0800","caller":"controllers/vote.go:14","msg":"收到投票请求"}
{"level":"INFO","time":"2025-08-26T00:10:01.515+0800","caller":"controllers/vote.go:23","msg":"用户ID获取成功","user_id":505376604688384}
{"level":"INFO","time":"2025-08-26T00:10:01.515+0800","caller":"controllers/vote.go:32","msg":"参数绑定成功","post_id":1684799886462976,"direction":0}
{"level":"INFO","time":"2025-08-26T00:10:01.515+0800","caller":"controllers/vote.go:50","msg":"开始调用投票业务逻辑"}
{"level":"INFO","time":"2025-08-26T00:10:01.516+0800","caller":"redis/vote.go:56","msg":"Redis投票开始","post_id":1684799886462976,"user_id":505376604688384,"direction":0}
{"level":"INFO","time":"2025-08-26T00:10:01.516+0800","caller":"redis/vote.go:73","msg":"Redis连接正常"}
{"level":"INFO","time":"2025-08-26T00:10:01.516+0800","caller":"redis/vote.go:82","msg":"获取到用户之前的投票状态","old_direction":-1,"new_direction":0}
{"level":"INFO","time":"2025-08-26T00:10:01.516+0800","caller":"redis/vote.go:135","msg":"Redis投票操作成功","post_id":1684799886462976,"user_id":505376604688384,"old_direction":-1,"new_direction":0,"vote_change":1,"score_change":2990.7333555555556}
{"level":"INFO","time":"2025-08-26T00:10:01.517+0800","caller":"controllers/vote.go:62","msg":"投票成功","user_id":505376604688384,"post_id":1684799886462976,"direction":0,"vote_num":0,"user_vote":0}
{"level":"INFO","time":"2025-08-26T00:10:01.517+0800","caller":"logger/logger.go:94","msg":"/api/v1/vote","status":200,"method":"POST","path":"/api/v1/vote","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0021509}
{"level":"INFO","time":"2025-08-26T00:10:02.577+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0057209}
{"level":"INFO","time":"2025-08-26T00:10:45.455+0800","caller":"web_app/main.go:99","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-26T00:10:45.455+0800","caller":"web_app/main.go:110","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-26T00:10:45.455+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-26T00:10:45.455+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-26T00:10:45.455+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-26T00:25:32.442+0800","caller":"web_app/main.go:64","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-26T00:25:32.456+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-26T00:25:32.457+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-26T00:25:32.457+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-26T00:25:32.458+0800","caller":"web_app/main.go:107","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-26T00:25:53.240+0800","caller":"logger/logger.go:94","msg":"/swagger/index.html","status":200,"method":"GET","path":"/swagger/index.html","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005429}
{"level":"INFO","time":"2025-08-26T00:25:53.362+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui.css","status":200,"method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.000512}
{"level":"INFO","time":"2025-08-26T00:25:53.367+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-standalone-preset.js","status":200,"method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0040998}
{"level":"INFO","time":"2025-08-26T00:25:53.372+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-bundle.js","status":200,"method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0096583}
{"level":"INFO","time":"2025-08-26T00:25:53.889+0800","caller":"logger/logger.go:94","msg":"/swagger/doc.json","status":200,"method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0007186}
{"level":"INFO","time":"2025-08-26T00:25:53.894+0800","caller":"logger/logger.go:94","msg":"/swagger/favicon-32x32.png","status":200,"method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-26T00:26:19.964+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:26:19.964+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":200,"method":"GET","path":"/api/v1/post/1","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0094868}
{"level":"ERROR","time":"2025-08-26T00:26:21.856+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:26:21.856+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1","status":200,"method":"GET","path":"/api/v1/post/1","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0016352}
{"level":"ERROR","time":"2025-08-26T00:27:47.205+0800","caller":"controllers/user.go:99","msg":"logic.Login failed","error":"用户不存在"}
{"level":"INFO","time":"2025-08-26T00:27:47.205+0800","caller":"logger/logger.go:94","msg":"/api/v1/login","status":200,"method":"POST","path":"/api/v1/login","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0027987}
{"level":"INFO","time":"2025-08-26T00:28:58.144+0800","caller":"logger/logger.go:94","msg":"/swagger/index.html","status":200,"method":"GET","path":"/swagger/index.html","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:28:58.178+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui.css","status":304,"method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:28:58.179+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-bundle.js","status":304,"method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:28:58.180+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-standalone-preset.js","status":304,"method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:28:58.553+0800","caller":"logger/logger.go:94","msg":"/swagger/doc.json","status":200,"method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:28:58.558+0800","caller":"logger/logger.go:94","msg":"/swagger/favicon-32x32.png","status":304,"method":"GET","path":"/swagger/favicon-32x32.png","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:29:09.594+0800","caller":"logger/logger.go:94","msg":"/api/v1/post","status":200,"method":"POST","path":"/api/v1/post","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-26T00:29:26.783+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:29:26.783+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1121","status":200,"method":"GET","path":"/api/v1/post/1121","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0019299}
{"level":"ERROR","time":"2025-08-26T00:29:30.146+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:29:30.146+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1121","status":200,"method":"GET","path":"/api/v1/post/1121","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0011326}
{"level":"INFO","time":"2025-08-26T00:30:00.000+0800","caller":"cron/score_updater.go:64","msg":"开始更新帖子评分"}
{"level":"INFO","time":"2025-08-26T00:30:00.014+0800","caller":"cron/score_updater.go:96","msg":"帖子评分更新完成","count":7}
{"level":"INFO","time":"2025-08-26T00:30:13.514+0800","caller":"logger/logger.go:94","msg":"/swagger/index.html","status":200,"method":"GET","path":"/swagger/index.html","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:30:13.566+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui.css","status":304,"method":"GET","path":"/swagger/swagger-ui.css","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:30:13.566+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-bundle.js","status":304,"method":"GET","path":"/swagger/swagger-ui-bundle.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:30:13.567+0800","caller":"logger/logger.go:94","msg":"/swagger/swagger-ui-standalone-preset.js","status":304,"method":"GET","path":"/swagger/swagger-ui-standalone-preset.js","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-26T00:30:13.818+0800","caller":"logger/logger.go:94","msg":"/swagger/doc.json","status":200,"method":"GET","path":"/swagger/doc.json","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-26T00:30:41.471+0800","caller":"web_app/main.go:45","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-26T00:30:41.483+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-26T00:30:41.484+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-26T00:30:41.484+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-26T00:30:41.485+0800","caller":"web_app/main.go:88","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-26T00:31:00.211+0800","caller":"logger/logger.go:94","msg":"/api/v1/posts2","status":200,"method":"GET","path":"/api/v1/posts2","query":"page=1&size=10&order=time","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0019185}
{"level":"INFO","time":"2025-08-26T00:31:03.580+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1692904057737216","status":200,"method":"GET","path":"/api/v1/post/1692904057737216","query":"","ip":"127.0.0.1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012349}
{"level":"INFO","time":"2025-08-26T00:31:17.606+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1692904057737216","status":200,"method":"GET","path":"/api/v1/post/1692904057737216","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.005568}
{"level":"ERROR","time":"2025-08-26T00:31:34.476+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:31:34.477+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/169290405773721","status":200,"method":"GET","path":"/api/v1/post/169290405773721","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0012834}
{"level":"ERROR","time":"2025-08-26T00:31:49.015+0800","caller":"controllers/post.go:67","msg":"GetPostDetailHandler failed","error":"sql: no rows in result set"}
{"level":"INFO","time":"2025-08-26T00:31:49.015+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/11111","status":200,"method":"GET","path":"/api/v1/post/11111","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0005043}
{"level":"INFO","time":"2025-08-26T00:32:00.816+0800","caller":"logger/logger.go:94","msg":"/api/v1/post/1692904057737216","status":200,"method":"GET","path":"/api/v1/post/1692904057737216","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0.0023844}
{"level":"INFO","time":"2025-08-26T00:33:17.410+0800","caller":"web_app/main.go:118","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-26T00:33:17.410+0800","caller":"web_app/main.go:129","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-26T00:33:17.411+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-26T00:33:17.411+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-26T00:33:17.411+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-28T17:38:52.690+0800","caller":"web_app/main.go:64","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-28T17:38:52.705+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3307,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-28T17:38:52.708+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-28T17:38:52.708+0800","caller":"cron/score_updater.go:51","msg":"定时任务启动成功"}
{"level":"INFO","time":"2025-08-28T17:38:52.709+0800","caller":"web_app/main.go:107","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-28T17:39:06.553+0800","caller":"logger/logger.go:94","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-28T17:39:06.770+0800","caller":"logger/logger.go:94","msg":"/favicon.ico","status":404,"method":"GET","path":"/favicon.ico","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-28T17:39:09.585+0800","caller":"logger/logger.go:94","msg":"/","status":404,"method":"GET","path":"/","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-28T17:39:33.131+0800","caller":"logger/logger.go:94","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","errors":"","cost":0.001504}
{"level":"INFO","time":"2025-08-28T17:39:59.172+0800","caller":"web_app/main.go:118","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-28T17:39:59.172+0800","caller":"web_app/main.go:129","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-28T17:39:59.172+0800","caller":"cron/score_updater.go:58","msg":"定时任务已停止"}
{"level":"INFO","time":"2025-08-28T17:39:59.173+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-28T17:39:59.173+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
