# BlueBell项目 Docker编排文件
#
# 端口映射说明:
# - MySQL: 本地3307 -> 容器3306 (避免与本地MySQL冲突)
# - Redis: 本地6379 -> 容器6379
# - Go应用: 本地8082 -> 容器8082
#
# 使用方法:
# docker-compose up -d    # 启动所有服务
# docker-compose down     # 停止所有服务
# docker-compose logs     # 查看日志

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bluebell_mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: web_app
    ports:
      - "3307:3306"  # 本地3307端口 -> 容器3306端口
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bluebell_redis
    ports:
      - "6379:6379"  # 本地6379端口 -> 容器6379端口
    volumes:
      - redis_data:/data

  # Go Web应用
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bluebell_web
    ports:
      - "8082:8082"  # 本地8082端口 -> 容器8082端口
    depends_on:
      - mysql
      - redis
    volumes:
      - ./web_app/logs:/app/logs



# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

# 网络
networks:
  bluebell_network:
    driver: bridge
