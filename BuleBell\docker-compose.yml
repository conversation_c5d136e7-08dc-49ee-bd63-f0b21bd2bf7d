services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bluebell_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: web_app
    ports:
      - "3307:3306"  # 使用3307端口避免冲突
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - bluebell_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bluebell_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bluebell_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Go Web应用
  web:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: bluebell_web
    restart: unless-stopped
    ports:
      - "8082:8082"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - GIN_MODE=release
    volumes:
      - ./web_app/logs:/app/logs
    networks:
      - bluebell_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8082/ping"]
      interval: 30s
      timeout: 10s
      retries: 3



# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

# 网络
networks:
  bluebell_network:
    driver: bridge
