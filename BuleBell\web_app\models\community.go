package models

import (
	"time"
)

type Community struct {
	CommunityID   int64     `json:"id" db:"community_id" binding:"required"`
	CommunityName string    `json:"name" db:"community_name" binding:"required"`
	Introduction  string    `json:"introduction" db:"introduction" binding:"required"`
	CreateTime    time.Time `json:"create_time" db:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time"`
}

// CommunityDetail 社区详情
type CommunityDetail struct {
	CommunityID   int64     `json:"id" db:"community_id"`
	CommunityName string    `json:"name" db:"community_name"`
	Introduction  string    `json:"introduction" db:"introduction"`
	CreateTime    time.Time `json:"create_time" db:"create_time"`
}
