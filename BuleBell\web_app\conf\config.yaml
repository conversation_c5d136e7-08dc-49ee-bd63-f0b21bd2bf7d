app:
  name: "web_app"    # 项目名称
  mode: "release"        # 模式
  port: 8082         # 端口
  start_time: "2025-08-21" # 起始时间
  machine_id: 1            # 机器ID

log:
  level: "debug"                    # 日志级别
  filename: "logs/web_app.log"      # 文件名
  max_size: 100                     # 最大大小
  max_backups: 10                   # 最大备份数
  max_age: 30                       # 最大保存天数
  compress: true                    # 是否压缩

mysql:
  host: "127.0.0.1"        # 主机
  port: 3307               # 端口 (Docker容器映射的端口)
  user: "root"             # 用户名
  password: "123456"       # 密码
  database: "web_app"      # 数据库
  max_open_conns: 100      # 最大连接数
  max_idle_conns: 20       # 最大空闲连接数

redis:
  host: "127.0.0.1"        # 主机
  port: 6379               # 端口
  password: ""             # 密码 (如果Redis没有设置密码，请保持为空)
  database: 0              # 数据库
  pool_size: 100           # 连接池大小