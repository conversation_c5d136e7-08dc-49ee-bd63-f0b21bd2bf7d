basePath: /api/v1
definitions:
  controllers.ResponseData:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  models.ParamLogin:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  models.ParamSignUp:
    properties:
      password:
        type: string
      re_password:
        type: string
      username:
        type: string
    required:
    - password
    - re_password
    - username
    type: object
  models.ParamVote:
    properties:
      direction:
        description: 投票方向：1=赞成，-1=反对，0=取消投票
        enum:
        - -1
        - 0
        - 1
        type: integer
      post_id:
        description: 帖子ID
        type: integer
    required:
    - post_id
    type: object
  models.Post:
    properties:
      author_id:
        type: integer
      community_id:
        type: integer
      content:
        type: string
      post_id:
        type: integer
      title:
        type: string
    required:
    - community_id
    - content
    - title
    type: object
host: localhost:8082
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: 这是一个使用Gin框架开发的Web应用API文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: BlueBell API
  version: "1.0"
paths:
  /login:
    post:
      consumes:
      - application/json
      description: 用户登录接口
      parameters:
      - description: 登录参数
        in: body
        name: object
        required: true
        schema:
          $ref: '#/definitions/models.ParamLogin'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      summary: 用户登录
      tags:
      - 用户相关接口
  /post:
    post:
      consumes:
      - application/json
      description: 创建新帖子接口
      parameters:
      - description: 帖子参数
        in: body
        name: object
        required: true
        schema:
          $ref: '#/definitions/models.Post'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      security:
      - ApiKeyAuth: []
      summary: 创建帖子
      tags:
      - 帖子相关接口
  /post/{id}:
    get:
      consumes:
      - application/json
      description: 根据帖子ID获取帖子详情
      parameters:
      - description: 帖子ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      summary: 获取帖子详情
      tags:
      - 帖子相关接口
  /posts2:
    get:
      consumes:
      - application/json
      description: 获取帖子列表，支持分页和排序
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: size
        type: integer
      - default: time
        description: 排序方式
        enum:
        - time
        - score
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      summary: 获取帖子列表
      tags:
      - 帖子相关接口
  /signup:
    post:
      consumes:
      - application/json
      description: 用户注册接口
      parameters:
      - description: 注册参数
        in: body
        name: object
        required: true
        schema:
          $ref: '#/definitions/models.ParamSignUp'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      summary: 用户注册
      tags:
      - 用户相关接口
  /vote:
    post:
      consumes:
      - application/json
      description: 用户为帖子投票接口
      parameters:
      - description: 投票参数
        in: body
        name: object
        required: true
        schema:
          $ref: '#/definitions/models.ParamVote'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/controllers.ResponseData'
      security:
      - ApiKeyAuth: []
      summary: 帖子投票
      tags:
      - 投票相关接口
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
