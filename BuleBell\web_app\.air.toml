# Air 配置文件 - Go 项目热重载工具
# 项目根目录，Air 将在此目录下监控文件变化
root = "."
# 测试数据目录，通常不需要监控
testdata_dir = "testdata"
# 临时文件目录，存放编译后的可执行文件
tmp_dir = "tmp"

[build]
  # 传递给编译后程序的命令行参数（数组格式）
  args_bin = []
  # 编译后的可执行文件路径和名称
  bin = "./tmp/web_app.exe"
  # 编译命令，将源码编译为可执行文件（包含Swagger文档生成）
  cmd = "swag init && go build -o ./tmp/web_app.exe ."
  # 文件变化后延迟多少毫秒再重新构建（避免频繁重建）
  delay = 1000 # 1秒
  # 排除监控的目录列表（这些目录的变化不会触发重建）
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "logs", "node_modules", "docs"]
  # 排除监控的特定文件列表
  exclude_file = []
  # 排除监控的文件正则表达式（测试文件通常不需要触发重建）
  exclude_regex = ["_test.go"]
  # 是否排除未修改的文件
  exclude_unchanged = false
  # 是否跟随符号链接
  follow_symlink = false
  # 完整的二进制文件路径（通常为空，使用 bin 配置）
  full_bin = ""
  # 额外包含监控的目录（通常为空，默认监控所有）
  include_dir = []
  # 监控的文件扩展名列表（只有这些类型的文件变化才触发重建）
  include_ext = ["go", "tpl", "tmpl", "html", "yaml"]
  # 杀死进程的延迟时间
  kill_delay = "0s"
  # 构建错误日志文件
  log = "build-errors.log"
  # 是否发送中断信号
  send_interrupt = false
  # 是否在根目录停止
  stop_on_root = false

# 终端输出颜色配置
[color]
  # 应用程序输出颜色（空表示使用默认）
  app = ""
  # 构建过程输出颜色
  build = "yellow"
  # 主要信息输出颜色
  main = "magenta"
  # 运行器输出颜色
  runner = "green"
  # 文件监控器输出颜色
  watcher = "cyan"

# 日志配置
[log]
  # 是否只显示主要日志（false表示显示所有日志）
  main_only = false
  # 是否在日志中显示时间戳
  time = true

# 杂项配置
[misc]
  # 退出时是否清理临时文件
  clean_on_exit = false

# 屏幕显示配置
[screen]
  # 重新构建时是否清屏（让输出更清晰）
  clear_on_rebuild = true
  # 是否保持滚动位置
  keep_scroll = true
