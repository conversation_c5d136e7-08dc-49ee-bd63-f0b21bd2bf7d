package mysql

import (
	"database/sql"
	"web_app/models"
)

func GetCommunityList() ([]*models.Community, error) {
	sqlStr := `select community_id, community_name from community order by community_id asc`
	var communities []*models.Community
	err := db.Select(&communities, sqlStr)
	if err != nil {
		return nil, err
	}
	return communities, nil
}

func GetCommunityDetail(communityID int64) (*models.CommunityDetail, error) {
	sqlStr := `select community_id, community_name, introduction, create_time from community where community_id = ?`
	var communityDetail models.CommunityDetail
	err := db.Get(&communityDetail, sqlStr, communityID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrorInvalidID
		}
		return nil, err
	}
	return &communityDetail, nil
}
