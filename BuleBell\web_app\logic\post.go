package logic

import (
	"time"
	"web_app/dao/mysql"
	"web_app/dao/redis"
	"web_app/models"
	"web_app/pkg/snowflake"

	"go.uber.org/zap"
)

func CreatePost(post *models.Post) (err error) {
	//生成post_id
	postID := snowflake.GenID()
	post.PostID = postID

	//存入数据库
	err = mysql.CreatePost(post)
	if err != nil {
		return err
	}

	// 初始化 Redis 评分（异步执行，失败不影响主流程）
	go func() {
		createTime := time.Now().Unix()
		if err := redis.InitPostScore(postID, createTime); err != nil {
			zap.L().Warn("初始化帖子Redis评分失败",
				zap.Int64("post_id", postID),
				zap.Error(err))
		}
	}()

	return nil
}

func GetPostDetail(postID int64) (post *models.ApiPostDetail, err error) {
	return mysql.GetPostDetailByID(postID)
}

// GetPostList 获取帖子列表，支持分页和排序
func GetPostList(page, size int64, order string) (posts []*models.ApiPostDetail, err error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	if order == "" {
		order = "time"
	}

	// 调用数据访问层
	posts, err = mysql.GetPostList(page, size, order)
	if err != nil {
		zap.L().Error("GetPostList failed", zap.Error(err))
		return nil, err
	}

	return posts, nil
}

// GetPostListWithVotes 获取带投票信息的帖子列表
func GetPostListWithVotes(page, size int64, order string, userID int64) (posts []*models.ApiPostDetail, err error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	if order == "" {
		order = "time"
	}

	// 根据排序方式获取帖子列表
	if order == "score" {
		// 按评分排序，从 Redis 获取排序后的帖子ID
		postIDs, err := redis.GetPostsByScore((page-1)*size, page*size-1)
		if err != nil || len(postIDs) == 0 {
			// Redis 获取失败，降级到数据库查询
			zap.L().Warn("Redis获取评分排序失败，降级到数据库查询", zap.Error(err))
			return GetPostList(page, size, order)
		}

		// 根据ID列表获取帖子详情
		posts, err = mysql.GetPostsByIDs(postIDs)
		if err != nil {
			zap.L().Error("根据ID列表获取帖子失败", zap.Error(err))
			return nil, err
		}
	} else {
		// 按时间排序，直接从数据库获取
		posts, err = mysql.GetPostList(page, size, order)
		if err != nil {
			zap.L().Error("GetPostList failed", zap.Error(err))
			return nil, err
		}
	}

	// 为每个帖子获取最新的投票信息
	for _, post := range posts {
		// 获取帖子的最新投票统计
		voteInfo, err := redis.GetPostVoteInfo(post.PostID)
		if err != nil {
			zap.L().Warn("获取帖子投票统计失败",
				zap.Int64("post_id", post.PostID),
				zap.Error(err))
			// 保持MySQL中的原始数据
		} else {
			// 使用Redis中的最新投票数据
			post.VoteNum = voteInfo.VoteNum
		}

		// 如果用户已登录，获取用户的投票状态
		if userID > 0 {
			userVote, err := redis.GetUserVote(userID, post.PostID)
			if err != nil {
				zap.L().Warn("获取用户投票状态失败",
					zap.Int64("user_id", userID),
					zap.Int64("post_id", post.PostID),
					zap.Error(err))
				userVote = 0 // 默认未投票
			}
			post.UserVote = userVote
		}
	}

	return posts, nil
}
