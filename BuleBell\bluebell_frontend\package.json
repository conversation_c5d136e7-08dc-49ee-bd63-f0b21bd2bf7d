{"name": "bluebell_frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "core-js": "^3.6.4", "vue": "^2.6.11", "vue-router": "^3.1.6", "vuex": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "babel-eslint": "^10.1.0", "css-loader": "^3.5.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "install": "^0.13.0", "less": "^3.11.0", "less-loader": "^5.0.0", "npm": "^6.14.4", "style-loader": "^1.1.3", "sudo": "^1.0.3", "vue-template-compiler": "^2.6.11"}}