<template>
  <div class="main">
    <div class="container">
      <h2 class="form-title">注册</h2>
      <div class="form-group">
        <label for="name">用户名</label>
        <input type="text" class="form-control" name="name" id="name" placeholder="用户名" v-model="username"/>
      </div>
      <div class="form-group">
        <label for="pass">密码</label>
        <input type="password" class="form-control" name="pass" id="pass" placeholder="密码" v-model="password"/>
      </div>
      <div class="form-group">
        <label for="re_pass">确认密码</label>
        <input type="password" class="form-control" name="re_pass" id="re_pass" placeholder="确认密码"  v-model="confirm_password"/>
      </div>
      <div class="form-btn">
        <button type="button" class="btn btn-info" @click="submit">提交</button>
      </div>
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
	name: "SignUp",
	data() {
		return {
			username: "",
			password: "",
			confirm_password: "",
			submitted: false,
			errorMessage: "",
			successMessage: ""
		};
	},
	computed: {
	},
	created() {

	},
	methods: {
		submit() {
			this.errorMessage = ""; // 清除之前的错误信息
			this.successMessage = ""; // 清除之前的成功信息

			// 简单的前端验证
			if (!this.username || !this.password || !this.confirm_password) {
				this.errorMessage = "请填写所有字段";
				return;
			}

			if (this.password !== this.confirm_password) {
				this.errorMessage = "两次输入的密码不一致";
				return;
			}

			this.$axios({
				method: 'post',
				url:'/signup',
				data: JSON.stringify({
					username: this.username,
					password: this.password,
					re_password: this.confirm_password
				})
			}).then((res)=>{
				console.log(res.data);
				if (res.code == 1000) {
          this.successMessage = "注册成功！3秒后跳转到登录页面...";
          setTimeout(() => {
            this.$router.push({ name: "Login" });
          }, 3000);
				}else{
          this.errorMessage = res.message || "注册失败，请重试";
        }
			}).catch((error)=>{
				console.log(error)
				this.errorMessage = "网络错误，请稍后重试";
			})
		}
	}
};
</script>
<style lang="less" scoped>
.main {
  background: #f8f8f8;
  padding: 150px 0;
  .container {
    width: 600px;
    background: #fff;
    margin: 0 auto;
    max-width: 1200px;
    padding: 20px;
    .form-title {
      margin-bottom: 33px;
      text-align: center;
    }
    .form-group {
      margin: 15px;
      label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 700;
      }
      .form-control {
        display: block;
        width: 100%;
        height: 34px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
    }
    .form-btn {
      display: flex;
      justify-content: center;
      .btn {
        padding: 6px 20px;
        font-size: 18px;
        line-height: 1.3333333;
        border-radius: 6px;
        display: inline-block;
        margin-bottom: 0;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        cursor: pointer;
        border: 1px solid transparent;
      }
      .btn-info {
        color: #fff;
        background-color: #5bc0de;
        border-color: #46b8da;
      }
    }
    .error-message {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      text-align: center;
    }
    .success-message {
      margin-top: 15px;
      padding: 10px;
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
      text-align: center;
    }
  }
}
</style>