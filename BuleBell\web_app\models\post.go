package models

type Post struct {
	Title       string `json:"title" db:"title" binding:"required"`
	Content     string `json:"content" db:"content" binding:"required"`
	CommunityID int64  `json:"community_id" db:"community_id" binding:"required"`
	AuthorID    int64  `json:"author_id" db:"author_id" `
	PostID      int64  `json:"post_id" db:"post_id" `
}

// ApiPostDetail 用于接收数据库查询结果的平铺结构
type ApiPostDetail struct {
	PostID        int64   `json:"post_id" db:"post_id"`
	Title         string  `json:"title" db:"title"`
	Content       string  `json:"content" db:"content"`
	AuthorID      int64   `json:"author_id" db:"author_id"`
	CommunityID   int64   `json:"community_id" db:"community_id"`
	AuthorName    string  `json:"author_name" db:"author_name"`
	CommunityName string  `json:"community_name" db:"community_name"`
	VoteNum       int64   `json:"vote_num" db:"vote_num"` // 投票总数
	Score         float64 `json:"score" db:"score"`       // 帖子评分
	UserVote      int8    `json:"user_vote"`              // 当前用户投票状态（不从数据库查询，后续填充）
}
