/*
Package cron 实现定时任务功能

主要功能：
1. 定时更新帖子热度评分
2. 清理过期的投票缓存
3. 同步 Redis 和 MySQL 数据

使用 robfig/cron 库实现定时任务调度

作者: DancingCircles
项目: Goweb-Frame
*/
package cron

import (
	"web_app/dao/mysql"
	"web_app/dao/redis"
	"web_app/models"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

var c *cron.Cron

// Init 初始化定时任务
func Init() {
	c = cron.New()
	
	// 每10分钟更新一次帖子评分
	_, err := c.AddFunc("*/10 * * * *", UpdatePostScores)
	if err != nil {
		zap.L().Error("添加评分更新定时任务失败", zap.Error(err))
	}
	
	// 每小时同步一次 Redis 和 MySQL 数据
	_, err = c.AddFunc("0 * * * *", SyncVoteData)
	if err != nil {
		zap.L().Error("添加数据同步定时任务失败", zap.Error(err))
	}
	
	// 每天凌晨清理过期数据
	_, err = c.AddFunc("0 0 * * *", CleanExpiredData)
	if err != nil {
		zap.L().Error("添加数据清理定时任务失败", zap.Error(err))
	}
	
	// 启动定时任务
	c.Start()
	zap.L().Info("定时任务启动成功")
}

// Stop 停止定时任务
func Stop() {
	if c != nil {
		c.Stop()
		zap.L().Info("定时任务已停止")
	}
}

// UpdatePostScores 更新帖子评分
func UpdatePostScores() {
	zap.L().Info("开始更新帖子评分")
	
	// 获取需要更新的帖子列表（最近24小时有投票活动的帖子）
	postInfos, err := mysql.GetActivePostsForScoreUpdate()
	if err != nil {
		zap.L().Error("获取活跃帖子列表失败", zap.Error(err))
		return
	}
	
	if len(postInfos) == 0 {
		zap.L().Info("没有需要更新评分的帖子")
		return
	}
	
	// 批量更新 Redis 评分
	err = redis.BatchUpdateScores(postInfos)
	if err != nil {
		zap.L().Error("批量更新Redis评分失败", zap.Error(err))
		return
	}
	
	// 更新 MySQL 中的评分
	for _, info := range postInfos {
		score := calculatePostScore(info)
		err = mysql.UpdatePostScore(info.PostID, score)
		if err != nil {
			zap.L().Error("更新MySQL帖子评分失败",
				zap.Int64("post_id", info.PostID),
				zap.Error(err))
		}
	}
	
	zap.L().Info("帖子评分更新完成", zap.Int("count", len(postInfos)))
}

// SyncVoteData 同步投票数据
func SyncVoteData() {
	zap.L().Info("开始同步投票数据")
	
	// 获取 Redis 中的投票统计
	// 这里可以实现 Redis -> MySQL 的数据同步逻辑
	// 确保数据一致性
	
	zap.L().Info("投票数据同步完成")
}

// CleanExpiredData 清理过期数据
func CleanExpiredData() {
	zap.L().Info("开始清理过期数据")
	
	// 清理过期的投票缓存
	// 清理过期的会话数据
	// 清理过期的日志文件等
	
	zap.L().Info("过期数据清理完成")
}

// calculatePostScore 计算帖子评分
func calculatePostScore(info models.PostVoteInfo) float64 {
	// 使用简化的热度算法
	// Score = (upvotes - downvotes) + time_factor
	
	voteScore := float64(info.VoteNum)
	
	// 时间因子：新帖子有一定优势
	// 这里可以根据创建时间计算时间衰减
	timeFactor := 0.0 // 简化实现
	
	return voteScore + timeFactor
}
