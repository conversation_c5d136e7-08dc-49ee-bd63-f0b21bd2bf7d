package controllers

import (
	"strconv"
	"web_app/logic"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CommunityHandler(c *gin.Context) {
	//查询到所有的社区（community_id,community_name）以列表的形式返回
	data, err := logic.GetCommunityList()
	if err != nil {
		zap.L().Error("logic.GetCommunityList failed", zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy)) //不轻易把错误信息返回给前端
		return
	}
	ResponseSuccess(c, data)
}

func CommunityDetailHandler(c *gin.Context) {
	//1.获取社区id
	communityID := c.Param("id")
	communityIDInt, err := strconv.ParseInt(communityID, 10, 64)
	if err != nil {
		zap.L().Error("CommunityDetailHandler failed", zap.Error(err))
		ResponseError(c, CodeInvalidParam, GetMsg(CodeInvalidParam))
		return
	}
	//2.根据id查询社区详情
	data, err := logic.GetCommunityDetail(communityIDInt)
	if err != nil {
		zap.L().Error("CommunityDetailHandler failed", zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	ResponseSuccess(c, data)
}
