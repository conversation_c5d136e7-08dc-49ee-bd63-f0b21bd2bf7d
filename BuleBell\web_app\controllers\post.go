package controllers

import (
	"strconv"
	"web_app/logic"
	"web_app/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreatePostHandler 创建帖子接口
// @Summary 创建帖子
// @Description 创建新帖子接口
// @Tags 帖子相关接口
// @Accept json
// @Produce json
// @Param object body models.Post true "帖子参数"
// @Success 200 {object} controllers.ResponseData
// @Security ApiKeyAuth
// @Router /post [post]
func CreatePostHandler(c *gin.Context) {
	// 获取当前用户id
	userID, err := GetCurrentUserID(c)
	if err != nil {
		ResponseError(c, CodeNeedLogin, GetMsg(CodeNeedLogin))
		return
	}
	//1.获取参数及参数校验
	post := new(models.Post)
	if err := c.ShouldBindJSON(post); err != nil {
		zap.L().Error("CreatePostHandler failed", zap.Error(err))
		ResponseError(c, CodeInvalidParam, GetMsg(CodeInvalidParam))
		return
	}
	post.AuthorID = userID
	//2.创建帖子
	err = logic.CreatePost(post)
	if err != nil {
		zap.L().Error("CreatePostHandler failed", zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	ResponseSuccess(c, nil)
}

// GetPostDetailHandler 获取帖子详情接口
// @Summary 获取帖子详情
// @Description 根据帖子ID获取帖子详情
// @Tags 帖子相关接口
// @Accept json
// @Produce json
// @Param id path int true "帖子ID"
// @Success 200 {object} controllers.ResponseData
// @Router /post/{id} [get]
func GetPostDetailHandler(c *gin.Context) {
	// 获取帖子id
	postID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		zap.L().Error("GetPostDetailHandler failed", zap.Error(err))
		ResponseError(c, CodeInvalidParam, GetMsg(CodeInvalidParam))
		return
	}
	// 获取帖子详情
	post, err := logic.GetPostDetail(postID)
	if err != nil {
		zap.L().Error("GetPostDetailHandler failed", zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	ResponseSuccess(c, post)
}

// GetPostListHandler 获取帖子列表
// @Summary 获取帖子列表
// @Description 获取帖子列表，支持分页和排序
// @Tags 帖子相关接口
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param order query string false "排序方式" Enums(time, score) default(time)
// @Success 200 {object} controllers.ResponseData
// @Router /posts2 [get]
func GetPostListHandler(c *gin.Context) {
	// 获取分页参数
	page, size := getPageInfo(c)

	// 获取排序参数
	order := c.Query("order")
	if order == "" {
		order = "time" // 默认按时间排序
	}

	// 获取当前用户ID（可选，未登录用户也可以查看）
	userID, _ := GetCurrentUserID(c)

	// 获取带投票信息的帖子列表
	posts, err := logic.GetPostListWithVotes(page, size, order, userID)
	if err != nil {
		zap.L().Error("GetPostListHandler failed", zap.Error(err))
		ResponseError(c, CodeServerBusy, GetMsg(CodeServerBusy))
		return
	}
	ResponseSuccess(c, posts)
}

// getPageInfo 获取分页信息
func getPageInfo(c *gin.Context) (int64, int64) {
	pageStr := c.Query("page")
	sizeStr := c.Query("size")

	var (
		page int64 = 1
		size int64 = 10
	)

	if pageStr != "" {
		page, _ = strconv.ParseInt(pageStr, 10, 64)
	}
	if sizeStr != "" {
		size, _ = strconv.ParseInt(sizeStr, 10, 64)
	}

	return page, size
}
