package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"web_app/models"

	"go.uber.org/zap"
)

// VoteForPost 用户为帖子投票
func VoteForPost(userID, postID int64, direction int8) error {
	// 开始事务
	tx, err := db.Beginx()
	if err != nil {
		zap.L().Error("开始投票事务失败", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 1. 查询用户之前的投票记录
	var oldDirection int8
	var hasVoted bool
	sqlStr := `SELECT direction FROM vote WHERE user_id = ? AND post_id = ?`
	err = tx.Get(&oldDirection, sqlStr, userID, postID)
	if err != nil && err != sql.ErrNoRows {
		zap.L().Error("查询用户投票记录失败", zap.Error(err))
		return err
	}
	hasVoted = (err != sql.ErrNoRows)

	// 2. 计算投票数量变化
	var voteChange int64
	if direction == 0 {
		// 取消投票
		if hasVoted {
			voteChange = -int64(oldDirection)
			// 删除投票记录
			sqlStr = `DELETE FROM vote WHERE user_id = ? AND post_id = ?`
			_, err = tx.Exec(sqlStr, userID, postID)
			if err != nil {
				zap.L().Error("删除投票记录失败", zap.Error(err))
				return err
			}
		}
	} else {
		// 投票或修改投票
		if hasVoted {
			// 修改投票
			voteChange = int64(direction - oldDirection)
			sqlStr = `UPDATE vote SET direction = ?, update_time = CURRENT_TIMESTAMP WHERE user_id = ? AND post_id = ?`
			_, err = tx.Exec(sqlStr, direction, userID, postID)
			if err != nil {
				zap.L().Error("更新投票记录失败", zap.Error(err))
				return err
			}
		} else {
			// 新投票
			voteChange = int64(direction)
			sqlStr = `INSERT INTO vote (user_id, post_id, direction) VALUES (?, ?, ?)`
			_, err = tx.Exec(sqlStr, userID, postID, direction)
			if err != nil {
				zap.L().Error("插入投票记录失败", zap.Error(err))
				return err
			}
		}
	}

	// 3. 更新帖子的投票统计
	if voteChange != 0 {
		sqlStr = `UPDATE post SET vote_num = vote_num + ? WHERE post_id = ?`
		_, err = tx.Exec(sqlStr, voteChange, postID)
		if err != nil {
			zap.L().Error("更新帖子投票统计失败", zap.Error(err))
			return err
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		zap.L().Error("提交投票事务失败", zap.Error(err))
		return err
	}

	zap.L().Info("投票操作成功",
		zap.Int64("user_id", userID),
		zap.Int64("post_id", postID),
		zap.Int8("direction", direction),
		zap.Int64("vote_change", voteChange))

	return nil
}

// GetUserVote 获取用户对帖子的投票状态
func GetUserVote(userID, postID int64) (int8, error) {
	var direction int8
	sqlStr := `SELECT direction FROM vote WHERE user_id = ? AND post_id = ?`
	err := db.Get(&direction, sqlStr, userID, postID)
	if err == sql.ErrNoRows {
		return 0, nil // 未投票
	}
	if err != nil {
		zap.L().Error("查询用户投票状态失败",
			zap.Int64("user_id", userID),
			zap.Int64("post_id", postID),
			zap.Error(err))
		return 0, err
	}
	return direction, nil
}

// GetPostVoteInfo 获取帖子的投票统计信息
func GetPostVoteInfo(postID int64) (*models.PostVoteInfo, error) {
	info := &models.PostVoteInfo{
		PostID: postID,
	}

	// 查询投票统计
	sqlStr := `
		SELECT 
			COALESCE(SUM(CASE WHEN direction = 1 THEN 1 ELSE 0 END), 0) as up_votes,
			COALESCE(SUM(CASE WHEN direction = -1 THEN 1 ELSE 0 END), 0) as down_votes,
			COALESCE(SUM(direction), 0) as vote_num
		FROM vote 
		WHERE post_id = ?`

	err := db.Get(info, sqlStr, postID)
	if err != nil && err != sql.ErrNoRows {
		zap.L().Error("查询帖子投票统计失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return nil, err
	}

	// 查询帖子创建时间用于评分计算
	sqlStr = `SELECT UNIX_TIMESTAMP(create_time) as create_time FROM post WHERE post_id = ?`
	err = db.Get(&info.CreateTime, sqlStr, postID)
	if err != nil {
		zap.L().Error("查询帖子创建时间失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return nil, err
	}

	return info, nil
}

// GetPostVoteStats 批量获取帖子投票统计
func GetPostVoteStats(postIDs []int64) (map[int64]*models.PostVoteInfo, error) {
	if len(postIDs) == 0 {
		return make(map[int64]*models.PostVoteInfo), nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(postIDs))
	args := make([]any, len(postIDs))
	for i, id := range postIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	sqlStr := fmt.Sprintf(`
		SELECT
			post_id,
			COALESCE(SUM(CASE WHEN direction = 1 THEN 1 ELSE 0 END), 0) as up_votes,
			COALESCE(SUM(CASE WHEN direction = -1 THEN 1 ELSE 0 END), 0) as down_votes,
			COALESCE(SUM(direction), 0) as vote_num
		FROM vote
		WHERE post_id IN (%s)
		GROUP BY post_id`,
		strings.Join(placeholders, ","))

	var stats []models.PostVoteInfo
	err := db.Select(&stats, sqlStr, args...)
	if err != nil {
		zap.L().Error("批量查询帖子投票统计失败", zap.Error(err))
		return nil, err
	}

	// 转换为map
	result := make(map[int64]*models.PostVoteInfo)
	for i := range stats {
		result[stats[i].PostID] = &stats[i]
	}

	// 为没有投票记录的帖子创建默认统计
	for _, postID := range postIDs {
		if _, exists := result[postID]; !exists {
			result[postID] = &models.PostVoteInfo{
				PostID:    postID,
				UpVotes:   0,
				DownVotes: 0,
				VoteNum:   0,
			}
		}
	}

	return result, nil
}

// UpdatePostScore 更新帖子评分
func UpdatePostScore(postID int64, score float64) error {
	sqlStr := `UPDATE post SET score = ? WHERE post_id = ?`
	_, err := db.Exec(sqlStr, score, postID)
	if err != nil {
		zap.L().Error("更新帖子评分失败",
			zap.Int64("post_id", postID),
			zap.Float64("score", score),
			zap.Error(err))
		return err
	}
	return nil
}

// GetPostCreateTime 获取帖子创建时间戳
func GetPostCreateTime(postID int64) (int64, error) {
	var createTime int64
	sqlStr := `SELECT UNIX_TIMESTAMP(create_time) FROM post WHERE post_id = ?`
	err := db.Get(&createTime, sqlStr, postID)
	if err != nil {
		zap.L().Error("查询帖子创建时间失败",
			zap.Int64("post_id", postID),
			zap.Error(err))
		return 0, err
	}
	return createTime, nil
}

// GetUserVotesForPosts 批量获取用户对多个帖子的投票状态
func GetUserVotesForPosts(userID int64, postIDs []int64) (map[int64]int8, error) {
	if len(postIDs) == 0 {
		return make(map[int64]int8), nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(postIDs))
	args := make([]any, len(postIDs)+1)
	args[0] = userID
	for i, id := range postIDs {
		placeholders[i] = "?"
		args[i+1] = id
	}

	sqlStr := fmt.Sprintf(`
		SELECT post_id, direction
		FROM vote
		WHERE user_id = ? AND post_id IN (%s)`,
		strings.Join(placeholders, ","))

	var votes []struct {
		PostID    int64 `db:"post_id"`
		Direction int8  `db:"direction"`
	}

	err := db.Select(&votes, sqlStr, args...)
	if err != nil {
		zap.L().Error("批量查询用户投票状态失败",
			zap.Int64("user_id", userID),
			zap.Error(err))
		return nil, err
	}

	// 转换为map
	result := make(map[int64]int8)
	for _, vote := range votes {
		result[vote.PostID] = vote.Direction
	}

	return result, nil
}

// GetPostsByIDs 根据ID列表获取帖子详情
func GetPostsByIDs(postIDs []int64) ([]*models.ApiPostDetail, error) {
	if len(postIDs) == 0 {
		return []*models.ApiPostDetail{}, nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(postIDs))
	args := make([]any, len(postIDs))
	for i, id := range postIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	sqlStr := fmt.Sprintf(`
		SELECT p.post_id, p.title, p.content, p.author_id, p.community_id,
		       p.vote_num, p.score,
		       COALESCE(u.username, '未知用户') as author_name,
		       COALESCE(c.community_name, '未知社区') as community_name
		FROM post p
		LEFT JOIN user u ON p.author_id = u.user_id
		LEFT JOIN community c ON p.community_id = c.community_id
		WHERE p.post_id IN (%s) AND (p.status = 1 OR p.status IS NULL)
		ORDER BY FIELD(p.post_id, %s)`,
		strings.Join(placeholders, ","),
		strings.Join(placeholders, ","))

	// 参数需要重复一次用于ORDER BY FIELD
	allArgs := make([]any, len(args)*2)
	copy(allArgs, args)
	copy(allArgs[len(args):], args)

	posts := make([]*models.ApiPostDetail, 0)
	err := db.Select(&posts, sqlStr, allArgs...)
	if err != nil {
		zap.L().Error("根据ID列表查询帖子失败", zap.Error(err))
		return nil, err
	}

	return posts, nil
}

// GetActivePostsForScoreUpdate 获取需要更新评分的活跃帖子
func GetActivePostsForScoreUpdate() ([]models.PostVoteInfo, error) {
	// 获取最近24小时有投票活动的帖子
	sqlStr := `
		SELECT DISTINCT p.post_id,
		       COALESCE(SUM(CASE WHEN v.direction = 1 THEN 1 ELSE 0 END), 0) as up_votes,
		       COALESCE(SUM(CASE WHEN v.direction = -1 THEN 1 ELSE 0 END), 0) as down_votes,
		       COALESCE(SUM(v.direction), 0) as vote_num,
		       UNIX_TIMESTAMP(p.create_time) as create_time
		FROM post p
		LEFT JOIN vote v ON p.post_id = v.post_id
		WHERE p.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
		   OR v.update_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
		GROUP BY p.post_id
		ORDER BY vote_num DESC
		LIMIT 100`

	var posts []models.PostVoteInfo
	err := db.Select(&posts, sqlStr)
	if err != nil {
		zap.L().Error("获取活跃帖子失败", zap.Error(err))
		return nil, err
	}

	return posts, nil
}
