BlueBell Docker 端口映射

=== 服务端口 ===
Go应用:  localhost:8082
MySQL:   localhost:3307  (用户: root, 密码: 123456, 数据库: web_app)
Redis:   localhost:6379

=== 常用命令 ===
启动: docker-compose up -d
停止: docker-compose down
日志: docker-compose logs
状态: docker ps

=== 测试地址 ===
健康检查: http://localhost:8082/ping
API文档:  http://localhost:8082/swagger/index.html

=== 注意事项 ===
- MySQL使用3307端口避免与本地MySQL冲突
- 所有数据会持久化保存
- 容器间通过服务名通信(mysql, redis, web)
