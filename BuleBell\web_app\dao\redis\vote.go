/*
Package redis/vote 实现基于 Redis 的高性能投票系统

核心功能：
1. 投票数据缓存 - 使用 Hash 存储帖子投票统计
2. 用户投票记录 - 使用 Hash 存储用户投票状态
3. 热门帖子排行 - 使用 Sorted Set 按评分排序
4. 投票算法 - 实现类似 Reddit 的热度算法

Redis 数据结构设计：
- post:vote:{post_id} -> Hash {up_votes, down_votes, vote_num}
- user:vote:{user_id} -> Hash {post_id: direction}
- post:score -> Sorted Set {post_id: score}
- post:time -> Sorted Set {post_id: timestamp}

算法说明：
使用改进的 Reddit 热度算法：
Score = log10(max(|ups - downs|, 1)) + (timestamp - epoch) / 45000
- 投票数量的对数增长，避免老帖子被新帖子完全压制
- 时间衰减因子，新帖子有一定优势
- 45000秒 ≈ 12.5小时，每12.5小时热度自然衰减一个数量级

作者: DancingCircles
项目: Goweb-Frame
*/
package redis

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"web_app/models"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	// Redis Key 前缀
	KeyPostVotePrefix = "post:vote:" // 帖子投票统计 Hash
	KeyUserVotePrefix = "user:vote:" // 用户投票记录 Hash
	KeyPostScore      = "post:score" // 帖子评分排行 Sorted Set
	KeyPostTime       = "post:time"  // 帖子时间排行 Sorted Set

	// 算法常量
	VoteScore  = 432.0      // 每票的基础分值 (24*60*60/200 = 432)
	ScoreDecay = 45000      // 评分衰减时间常数（秒）
	EpochTime  = 1640995200 // 算法起始时间戳 (2022-01-01 00:00:00 UTC)
)

// VoteForPost 用户为帖子投票
func VoteForPost(postID, userID int64, direction int8, createTime int64) error {
	ctx := context.Background()

	zap.L().Info("Redis投票开始",
		zap.Int64("post_id", postID),
		zap.Int64("user_id", userID),
		zap.Int8("direction", direction))

	// 测试 Redis 连接
	if rdb == nil {
		zap.L().Error("Redis客户端未初始化")
		return fmt.Errorf("Redis客户端未初始化")
	}

	// 测试 Redis 连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		zap.L().Error("Redis连接测试失败", zap.Error(err))
		return fmt.Errorf("Redis连接失败: %v", err)
	}
	zap.L().Info("Redis连接正常")

	// 1. 获取用户之前的投票状态
	oldDirection, err := GetUserVote(userID, postID)
	if err != nil && err != redis.Nil {
		zap.L().Error("获取用户投票状态失败", zap.Error(err))
		return err
	}

	zap.L().Info("获取到用户之前的投票状态",
		zap.Int8("old_direction", oldDirection),
		zap.Int8("new_direction", direction))

	// 2. 计算投票变化
	voteChange := int64(direction - oldDirection)
	if voteChange == 0 {
		return nil // 没有变化，直接返回
	}

	// 3. 使用 Pipeline 批量执行 Redis 操作
	pipe := rdb.Pipeline()

	// 更新帖子投票统计
	postVoteKey := KeyPostVotePrefix + strconv.FormatInt(postID, 10)

	if direction == 0 {
		// 取消投票 - 删除用户投票记录
		userVoteKey := KeyUserVotePrefix + strconv.FormatInt(userID, 10)
		pipe.HDel(ctx, userVoteKey, strconv.FormatInt(postID, 10))
	} else {
		// 投票或修改投票 - 更新用户投票记录
		userVoteKey := KeyUserVotePrefix + strconv.FormatInt(userID, 10)
		pipe.HSet(ctx, userVoteKey, strconv.FormatInt(postID, 10), direction)
	}

	// 更新投票统计
	if voteChange > 0 {
		// 增加赞成票
		pipe.HIncrBy(ctx, postVoteKey, "up_votes", voteChange)
	} else {
		// 增加反对票
		pipe.HIncrBy(ctx, postVoteKey, "down_votes", -voteChange)
	}
	pipe.HIncrBy(ctx, postVoteKey, "vote_num", voteChange)

	// 4. 计算新的评分
	newScore := calculateScore(voteChange, createTime)

	// 更新评分排行
	pipe.ZIncrBy(ctx, KeyPostScore, newScore, strconv.FormatInt(postID, 10))

	// 5. 执行 Pipeline
	_, err = pipe.Exec(ctx)
	if err != nil {
		zap.L().Error("Redis投票操作失败",
			zap.Int64("post_id", postID),
			zap.Int64("user_id", userID),
			zap.Int8("direction", direction),
			zap.Error(err))
		return err
	}

	zap.L().Info("Redis投票操作成功",
		zap.Int64("post_id", postID),
		zap.Int64("user_id", userID),
		zap.Int8("old_direction", oldDirection),
		zap.Int8("new_direction", direction),
		zap.Int64("vote_change", voteChange),
		zap.Float64("score_change", newScore))

	return nil
}

// GetUserVote 获取用户对帖子的投票状态
func GetUserVote(userID, postID int64) (int8, error) {
	ctx := context.Background()
	userVoteKey := KeyUserVotePrefix + strconv.FormatInt(userID, 10)

	result, err := rdb.HGet(ctx, userVoteKey, strconv.FormatInt(postID, 10)).Result()
	if err == redis.Nil {
		return 0, nil // 未投票
	}
	if err != nil {
		return 0, err
	}

	direction, err := strconv.ParseInt(result, 10, 8)
	if err != nil {
		zap.L().Error("解析投票方向失败", zap.String("result", result), zap.Error(err))
		return 0, err
	}

	return int8(direction), nil
}

// GetPostVoteInfo 获取帖子投票统计信息
func GetPostVoteInfo(postID int64) (*models.PostVoteInfo, error) {
	ctx := context.Background()
	postVoteKey := KeyPostVotePrefix + strconv.FormatInt(postID, 10)

	// 使用 HMGet 批量获取投票统计
	result, err := rdb.HMGet(ctx, postVoteKey, "up_votes", "down_votes", "vote_num").Result()
	if err != nil {
		return nil, err
	}

	info := &models.PostVoteInfo{
		PostID: postID,
	}

	// 解析结果
	if result[0] != nil {
		if upVotes, err := strconv.ParseInt(result[0].(string), 10, 64); err == nil {
			info.UpVotes = upVotes
		}
	}
	if result[1] != nil {
		if downVotes, err := strconv.ParseInt(result[1].(string), 10, 64); err == nil {
			info.DownVotes = downVotes
		}
	}
	if result[2] != nil {
		if voteNum, err := strconv.ParseInt(result[2].(string), 10, 64); err == nil {
			info.VoteNum = voteNum
		}
	}

	return info, nil
}

// GetPostsByScore 按评分获取帖子ID列表
func GetPostsByScore(start, stop int64) ([]int64, error) {
	ctx := context.Background()

	// 从高分到低分获取帖子ID
	result, err := rdb.ZRevRange(ctx, KeyPostScore, start, stop).Result()
	if err != nil {
		return nil, err
	}

	postIDs := make([]int64, len(result))
	for i, idStr := range result {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			postIDs[i] = id
		}
	}

	return postIDs, nil
}

// InitPostScore 初始化帖子评分（用于新帖子）
func InitPostScore(postID int64, createTime int64) error {
	ctx := context.Background()

	// 计算初始评分（基于创建时间）
	score := calculateScore(0, createTime)

	pipe := rdb.Pipeline()

	// 添加到评分排行
	pipe.ZAdd(ctx, KeyPostScore, redis.Z{
		Score:  score,
		Member: strconv.FormatInt(postID, 10),
	})

	// 添加到时间排行
	pipe.ZAdd(ctx, KeyPostTime, redis.Z{
		Score:  float64(createTime),
		Member: strconv.FormatInt(postID, 10),
	})

	// 初始化投票统计
	postVoteKey := KeyPostVotePrefix + strconv.FormatInt(postID, 10)
	pipe.HMSet(ctx, postVoteKey, map[string]any{
		"up_votes":   0,
		"down_votes": 0,
		"vote_num":   0,
	})

	_, err := pipe.Exec(ctx)
	if err != nil {
		zap.L().Error("初始化帖子评分失败",
			zap.Int64("post_id", postID),
			zap.Int64("create_time", createTime),
			zap.Error(err))
		return err
	}

	return nil
}

// calculateScore 计算帖子评分
// 使用改进的 Reddit 热度算法
func calculateScore(voteChange int64, createTime int64) float64 {
	// 时间因子：新帖子有优势
	timeFactor := float64(createTime-EpochTime) / ScoreDecay

	// 投票因子：投票数量的影响
	voteFactor := float64(voteChange) * VoteScore

	// 总评分
	score := voteFactor + timeFactor

	return score
}

// BatchUpdateScores 批量更新帖子评分（定时任务使用）
func BatchUpdateScores(postInfos []models.PostVoteInfo) error {
	if len(postInfos) == 0 {
		return nil
	}

	ctx := context.Background()
	pipe := rdb.Pipeline()

	for _, info := range postInfos {
		// 重新计算评分
		score := calculateHotScore(info.VoteNum, info.CreateTime)

		// 更新评分排行
		pipe.ZAdd(ctx, KeyPostScore, redis.Z{
			Score:  score,
			Member: strconv.FormatInt(info.PostID, 10),
		})
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		zap.L().Error("批量更新评分失败", zap.Error(err))
		return err
	}

	return nil
}

// calculateHotScore 计算热度评分（用于定时更新）
func calculateHotScore(voteNum int64, createTime int64) float64 {
	// 投票数量的对数增长
	voteScore := math.Log10(math.Max(math.Abs(float64(voteNum)), 1))
	if voteNum < 0 {
		voteScore = -voteScore
	}

	// 时间衰减
	timeFactor := float64(createTime-EpochTime) / ScoreDecay

	return voteScore + timeFactor
}
