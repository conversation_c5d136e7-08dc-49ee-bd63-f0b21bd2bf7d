package logic

import (
	"web_app/dao/mysql"
	"web_app/models"
)

func GetCommunityList() ([]*models.Community, error) {
	//查询数据库，找到所有的community，并返回
	communities, err := mysql.GetCommunityList()
	if err != nil {
		return nil, err
	}
	return communities, nil
}

func GetCommunityDetail(communityID int64) (*models.CommunityDetail, error) {
	communityDetail, err := mysql.GetCommunityDetail(communityID)
	if err != nil {
		return nil, err
	}
	return communityDetail, nil
}
