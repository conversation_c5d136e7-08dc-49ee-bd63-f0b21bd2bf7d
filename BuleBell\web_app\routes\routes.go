// Package routes 负责HTTP路由配置和中间件管理
package routes

import (
	"net/http"
	"web_app/controllers"
	"web_app/logger"
	"web_app/middlewares"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupRouter 配置和初始化路由
func SetupRouter() *gin.Engine {

	// 创建路由引擎
	r := gin.New()

	// 配置全局中间件
	r.Use(logger.GinLogger(), logger.GinRecovery(true), middlewares.CORSMiddleware())

	// Swagger文档路由
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 创建API v1路由组
	v1 := r.Group("/api/v1")
	{
		// 注册业务路由
		v1.POST("/signup", controllers.SignUpHandler)
		v1.POST("/login", controllers.LoginHandler)
		v1.GET("/community", controllers.CommunityHandler)
		v1.GET("/community/:id", controllers.CommunityDetailHandler)
		v1.POST("/post", middlewares.JWTAuthMiddleware(), controllers.CreatePostHandler)
		v1.GET("/post/:id", controllers.GetPostDetailHandler)
		v1.GET("/post", controllers.GetPostListHandler)
		v1.GET("/posts2", controllers.GetPostListHandler) // 兼容前端调用

		// 投票相关接口
		v1.POST("/vote", middlewares.JWTAuthMiddleware(), controllers.VoteHandler)
		v1.GET("/vote/:id", controllers.GetPostVoteHandler)

		// 健康检查接口（需要JWT认证）
		v1.GET("/ping", middlewares.JWTAuthMiddleware(), func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
				"status":  "healthy",
				"service": "Goweb-Frame",
			})
		})
	}

	// 返回配置完成的路由引擎
	return r
}
