@echo off
echo 正在下载Docker镜像...

echo.
echo [1/3] 下载MySQL镜像...
docker pull mysql:8.0
if %errorlevel% neq 0 (
    echo MySQL镜像下载失败，尝试其他版本...
    docker pull mysql:latest
)

echo.
echo [2/3] 下载Redis镜像...
docker pull redis:7-alpine
if %errorlevel% neq 0 (
    echo Redis镜像下载失败，尝试其他版本...
    docker pull redis:alpine
)

echo.
echo [3/3] 下载Golang镜像...
docker pull golang:1.21-alpine
if %errorlevel% neq 0 (
    echo Golang镜像下载失败，尝试其他版本...
    docker pull golang:alpine
)

echo.
echo 镜像下载完成！
echo 现在可以运行: docker-compose up -d

pause
