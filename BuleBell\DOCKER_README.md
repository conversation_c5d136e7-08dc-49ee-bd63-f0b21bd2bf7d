# BlueBell Docker 部署指南

本文档介绍如何使用Docker部署BlueBell项目。

## 📋 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 🚀 快速开始

### 1. 克隆项目并进入目录
```bash
cd BuleBell
```

### 2. 使用部署脚本（推荐）
```bash
# 给脚本执行权限
chmod +x docker-deploy.sh

# 启动所有服务
./docker-deploy.sh start

# 查看服务状态
./docker-deploy.sh status

# 查看日志
./docker-deploy.sh logs
```

### 3. 手动使用Docker Compose
```bash
# 构建并启动所有服务
docker-compose up -d --build

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:80
- **后端API**: http://localhost:8082
- **API文档**: http://localhost:8082/swagger/index.html
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 📁 项目结构

```
BuleBell/
├── Dockerfile                 # Go应用Docker文件
├── docker-compose.yml         # Docker编排文件
├── .dockerignore             # Docker忽略文件
├── docker-deploy.sh          # 部署脚本
├── init.sql                  # MySQL初始化脚本
├── web_app/                  # Go后端应用
└── bluebell_frontend/        # Vue前端应用
    ├── Dockerfile            # 前端Docker文件
    └── nginx.conf            # Nginx配置
```

## ⚙️ 配置说明

### 数据库配置
- **MySQL用户**: root
- **MySQL密码**: 123456
- **数据库名**: web_app
- **Root密码**: 123456

### Redis配置
- **端口**: 6379
- **无密码**

### 应用配置
项目已经创建了专门的Docker配置文件 `web_app/conf/config.docker.yaml`，其中包含了正确的容器服务名配置：

```yaml
mysql:
  host: mysql          # Docker容器服务名
  port: 3306
  user: root
  password: 123456
  database: web_app

redis:
  host: redis          # Docker容器服务名
  port: 6379
  password: ""
  database: 0
```

**注意**: Docker环境会自动使用 `config.docker.yaml` 配置文件，无需手动修改。

## 🛠️ 常用命令

### 部署脚本命令
```bash
./docker-deploy.sh start     # 启动服务
./docker-deploy.sh stop      # 停止服务
./docker-deploy.sh restart   # 重启服务
./docker-deploy.sh logs      # 查看日志
./docker-deploy.sh status    # 查看状态
./docker-deploy.sh clean     # 清理资源
```

### Docker Compose命令
```bash
# 重新构建特定服务
docker-compose build web

# 重启特定服务
docker-compose restart web

# 查看特定服务日志
docker-compose logs -f web

# 进入容器
docker-compose exec web sh
docker-compose exec mysql mysql -u root -p

# 查看资源使用情况
docker-compose top
```

## 🔧 故障排除

### 1. 端口冲突
如果端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8083:8082"  # 将8082改为其他端口
```

### 2. 数据库连接失败
- 检查MySQL容器是否正常启动
- 确认配置文件中的数据库连接信息
- 查看数据库日志：`docker-compose logs mysql`

### 3. 前端无法访问后端
- 检查nginx配置中的代理设置
- 确认后端服务正常运行
- 查看网络连接：`docker network ls`

### 4. 内存不足
- 增加Docker分配的内存
- 或者单独启动服务：
```bash
# 只启动后端服务
docker-compose up -d mysql redis web
```

## 📊 监控和日志

### 查看容器状态
```bash
docker-compose ps
docker stats
```

### 查看日志
```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f web
docker-compose logs -f mysql
```

### 数据持久化
数据存储在Docker卷中：
- `mysql_data`: MySQL数据
- `redis_data`: Redis数据
- `./web_app/logs`: 应用日志

## 🔄 更新部署

```bash
# 拉取最新代码后重新部署
git pull
./docker-deploy.sh restart
```

## 🧹 清理

```bash
# 停止并删除所有容器、网络、卷
./docker-deploy.sh clean

# 或手动清理
docker-compose down -v
docker system prune -f
```

## 📝 注意事项

1. **首次启动**: 可能需要几分钟来下载镜像和初始化数据库
2. **配置文件**: 项目会自动使用 `config.docker.yaml`，无需修改原配置
3. **健康检查**: 添加了 `/ping` 端点用于Docker健康检查
4. **端口访问**: 确保防火墙允许相应端口的访问
5. **密码安全**: 生产环境建议修改默认密码
6. **数据备份**: 定期备份数据库数据
7. **资源监控**: 监控容器资源使用情况
8. **日志查看**: 应用日志会保存在 `./web_app/logs` 目录

## 🆘 获取帮助

如果遇到问题，可以：
1. 查看容器日志定位问题
2. 检查Docker和Docker Compose版本
3. 确认系统资源是否充足
4. 参考官方Docker文档
