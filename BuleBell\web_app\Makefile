# BlueBell API 开发工具

.PHONY: docs dev run build clean help

# 生成Swagger文档
docs:
	@echo "正在生成Swagger文档..."
	@swag init
	@echo "Swagger文档生成完成！访问: http://localhost:8082/swagger/index.html"

# 开发模式（自动重载 + 文档生成）
dev: docs
	@echo "启动开发服务器..."
	@air

# 直接运行
run: docs
	@echo "启动服务器..."
	@go run main.go

# 构建生产版本
build: docs
	@echo "构建生产版本..."
	@go build -o bin/web_app main.go
	@echo "构建完成: bin/web_app"

# 清理临时文件
clean:
	@echo "清理临时文件..."
	@rm -rf tmp/ bin/ docs/
	@echo "清理完成"

# 帮助信息
help:
	@echo "可用命令:"
	@echo "  make docs  - 生成Swagger文档"
	@echo "  make dev   - 开发模式（自动重载）"
	@echo "  make run   - 直接运行服务器"
	@echo "  make build - 构建生产版本"
	@echo "  make clean - 清理临时文件"
	@echo "  make help  - 显示帮助信息"
