package mysql

import (
	"crypto/md5"
	"database/sql"
	"encoding/hex"
	"errors"
	"web_app/models"
)

//把每一步操作封装成函数
//待logic层根据业务需求调用

func CheckUserExist(username string) (exist bool, err error) {
	sqlStr := `select count(user_id) from user where username = ?`
	var count int64
	err = db.Get(&count, sqlStr, username)
	if err != nil {
		//查询数据库失败
		return false, err
	}
	exist = count > 0
	return
}

func InsertUser(user *models.User) (err error) {
	//对密码进行加密
	user.Password, err = encryptPassword(user.Password)
	if err != nil {
		return err
	}
	sqlStr := `insert into user(user_id, username, password) values(?, ?, ?)`
	_, err = db.Exec(sqlStr, user.UserID, user.Username, user.Password)
	return
}

func encryptPassword(password string) (string, error) {
	h := md5.New()
	h.Write([]byte(password))
	return hex.EncodeToString(h.Sum(nil)), nil
}

func Login(user *models.User) (err error) {
	// 保存用户输入的原始密码
	inputPassword := user.Password

	sqlStr := `select user_id, username, password from user where username = ?`
	err = db.Get(user, sqlStr, user.Username)
	if err == sql.ErrNoRows {
		return errors.New("用户不存在")
	}
	if err != nil {
		return err
	}

	// 对用户输入的密码进行加密
	encryptedInputPassword, err := encryptPassword(inputPassword)
	if err != nil {
		return err
	}

	// 比较加密后的输入密码和数据库中的密码
	if encryptedInputPassword != user.Password {
		return errors.New("密码错误")
	}
	return
}
